// 测试路径解析逻辑
const path = require('path');

function testPathResolution() {
    console.log('测试路径解析逻辑...');
    
    const configuredPath = './test-docs';
    const workspacePath = __dirname; // 模拟工作区路径
    
    console.log(`配置的路径: ${configuredPath}`);
    console.log(`工作区路径: ${workspacePath}`);
    
    // 检查是否为绝对路径
    const isAbsolute = path.isAbsolute(configuredPath);
    console.log(`是否为绝对路径: ${isAbsolute}`);
    
    if (!isAbsolute) {
        const resolvedPath = path.join(workspacePath, configuredPath);
        console.log(`解析后的绝对路径: ${resolvedPath}`);
        
        // 检查目录是否存在
        const fs = require('fs');
        const exists = fs.existsSync(resolvedPath);
        console.log(`目录是否存在: ${exists}`);
        
        if (exists) {
            const files = fs.readdirSync(resolvedPath);
            console.log(`目录中的文件: ${files.join(', ')}`);
        }
    }
    
    console.log('✅ 路径解析测试完成');
}

testPathResolution();
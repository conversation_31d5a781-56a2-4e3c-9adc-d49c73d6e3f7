#!/usr/bin/env node

/**
 * 创建简化的测试版本
 * 用于验证WebView基本功能是否正常
 */

const fs = require('fs');

const simpleHtml = `
    private _getHtmlForWebview(webview: vscode.Webview) {
        const nonce = this.getNonce();

        return \`<!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI自动化测试</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        color: var(--vscode-foreground);
                        background: var(--vscode-editor-background);
                        padding: 20px;
                    }
                    .test-button {
                        background: #007acc;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                        margin: 10px;
                    }
                    .test-button:hover {
                        background: #1177bb;
                    }
                    .success {
                        color: #22c55e;
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <h1>🧪 AI自动化测试</h1>
                <p>如果你能看到这个界面，说明WebView基本功能正常！</p>
                
                <div>
                    <button class="test-button" onclick="testFunction()">测试按钮</button>
                    <button class="test-button" onclick="sendMessage()">发送消息</button>
                </div>
                
                <div id="result"></div>
                
                <script nonce="\${nonce}">
                    const vscode = acquireVsCodeApi();
                    
                    function testFunction() {
                        document.getElementById('result').innerHTML = 
                            '<p class="success">✅ JavaScript功能正常！</p>';
                    }
                    
                    function sendMessage() {
                        vscode.postMessage({
                            command: 'test',
                            message: 'Hello from WebView!'
                        });
                        document.getElementById('result').innerHTML = 
                            '<p class="success">✅ 消息已发送到后端！</p>';
                    }
                    
                    window.addEventListener('message', event => {
                        const message = event.data;
                        if (message.command === 'testResponse') {
                            document.getElementById('result').innerHTML = 
                                '<p class="success">✅ 收到后端响应: ' + message.data + '</p>';
                        }
                    });
                </script>
            </body>
            </html>\`;
    }
`;

fs.writeFileSync('简化测试版本.txt', simpleHtml);

console.log('🧪 简化测试版本已创建！');
console.log('\n📋 使用方法:');
console.log('1. 复制 简化测试版本.txt 中的代码');
console.log('2. 临时替换 mainPanel.ts 中的 _getHtmlForWebview 方法');
console.log('3. 编译并测试');
console.log('4. 如果简化版本能正常显示，说明问题在于CSS/JS文件加载');
console.log('5. 如果简化版本也有问题，说明WebView配置有问题');

console.log('\n🎯 这个版本会显示:');
console.log('- 基本的HTML结构');
console.log('- VS Code主题变量');
console.log('- 简单的按钮交互');
console.log('- 前后端消息通信测试');
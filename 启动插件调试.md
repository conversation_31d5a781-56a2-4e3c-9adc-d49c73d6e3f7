# 如何查看WebView面板界面

## 方法1：VS Code开发调试模式

1. **打开项目**
   - 在VS Code中打开这个项目文件夹

2. **启动调试**
   - 按 `F5` 或者点击菜单 `Run > Start Debugging`
   - 这会打开一个新的VS Code扩展开发主机窗口

3. **打开WebView面板**
   - 在新窗口中，按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
   - 输入 "AI测试" 或 "AI Test"
   - 选择 "AI测试: 打开AI测试面板" 命令

4. **查看界面**
   - WebView面板会在侧边栏打开，显示我们实现的界面

## 方法2：安装打包好的插件

1. **安装插件**
   ```bash
   code --install-extension ai-test-0.0.1.vsix
   ```

2. **重启VS Code**

3. **打开面板**
   - 使用命令面板或点击状态栏的"AI测试"按钮

## 方法3：查看界面预览

如果无法运行插件，可以查看界面的HTML预览版本。
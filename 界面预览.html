<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI自动化测试 - 界面预览</title>
    <style>
        /* 模拟VS Code主题变量 */
        :root {
            --vscode-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --vscode-font-size: 13px;
            --vscode-foreground: #cccccc;
            --vscode-editor-background: #1e1e1e;
            --vscode-sideBar-background: #252526;
            --vscode-panel-border: #3c3c3c;
            --vscode-focusBorder: #007acc;
            --vscode-button-background: #0e639c;
            --vscode-button-foreground: #ffffff;
            --vscode-button-hoverBackground: #1177bb;
            --vscode-button-secondaryBackground: #3c3c3c;
            --vscode-button-secondaryForeground: #cccccc;
            --vscode-button-secondaryHoverBackground: #4c4c4c;
            --vscode-input-background: #3c3c3c;
            --vscode-input-foreground: #cccccc;
            --vscode-input-border: #3c3c3c;
            --vscode-list-hoverBackground: #2a2d2e;
            --vscode-list-activeSelectionBackground: #094771;
            --vscode-list-activeSelectionForeground: #ffffff;
            --vscode-descriptionForeground: #9d9d9d;
            --vscode-tab-activeBackground: #1e1e1e;
            --vscode-tab-activeForeground: #ffffff;
            --vscode-tab-inactiveBackground: #2d2d30;
            --vscode-tab-inactiveForeground: #969696;
            --vscode-tab-hoverBackground: #1e1e1e;
            --vscode-tab-hoverForeground: #ffffff;
            --vscode-testing-iconPassed: #73c991;
            --vscode-testing-iconFailed: #f14c4c;
            --vscode-testing-iconQueued: #cca700;
            --vscode-testing-iconUnset: #848484;
            --vscode-notifications-background: #252526;
            --vscode-notifications-border: #3c3c3c;
            --vscode-notifications-foreground: #cccccc;
            --vscode-scrollbarSlider-background: #79797966;
            --vscode-scrollbarSlider-hoverBackground: #646464b3;
            --vscode-scrollbarSlider-activeBackground: #bfbfbf66;
        }
        
        /* 浅色主题 */
        @media (prefers-color-scheme: light) {
            :root {
                --vscode-foreground: #333333;
                --vscode-editor-background: #ffffff;
                --vscode-sideBar-background: #f3f3f3;
                --vscode-panel-border: #e5e5e5;
                --vscode-focusBorder: #0078d4;
                --vscode-button-background: #0078d4;
                --vscode-button-hoverBackground: #106ebe;
                --vscode-button-secondaryBackground: #e1e1e1;
                --vscode-button-secondaryForeground: #333333;
                --vscode-button-secondaryHoverBackground: #d1d1d1;
                --vscode-input-background: #ffffff;
                --vscode-input-foreground: #333333;
                --vscode-input-border: #cecece;
                --vscode-list-hoverBackground: #f0f0f0;
                --vscode-list-activeSelectionBackground: #0078d4;
                --vscode-descriptionForeground: #717171;
                --vscode-tab-activeBackground: #ffffff;
                --vscode-tab-activeForeground: #333333;
                --vscode-tab-inactiveBackground: #ececec;
                --vscode-tab-inactiveForeground: #333333;
                --vscode-tab-hoverBackground: #ffffff;
                --vscode-tab-hoverForeground: #333333;
                --vscode-notifications-background: #f3f3f3;
                --vscode-notifications-border: #e5e5e5;
                --vscode-notifications-foreground: #333333;
            }
        }
    </style>
    <link rel="stylesheet" href="src/ui/assets/main.css">
</head>
<body class="vscode-body">
    <div class="container">
        <div class="header">
            <h1 class="title">AI自动化测试</h1>
            <div class="tabs">
                <button class="tab-button active" data-tab="tests">
                    <span class="tab-icon">🧪</span>
                    <span class="tab-text">测试</span>
                </button>
                <button class="tab-button" data-tab="config">
                    <span class="tab-icon">⚙️</span>
                    <span class="tab-text">配置</span>
                </button>
            </div>
        </div>
        
        <div id="tests" class="tab-content active">
            <div class="section">
                <div class="section-header">
                    <h3>测试文档管理</h3>
                    <div class="controls">
                        <button id="scanBtn" class="btn btn-primary">
                            <span class="btn-icon">🔍</span>
                            扫描文档
                        </button>
                        <button id="selectAllBtn" class="btn btn-secondary">
                            <span class="btn-icon">☑️</span>
                            全选
                        </button>
                        <button id="executeBtn" class="btn btn-success">
                            <span class="btn-icon">🚀</span>
                            执行测试
                        </button>
                        <button id="generateBtn" class="btn btn-secondary">
                            <span class="btn-icon">✨</span>
                            生成步骤
                        </button>
                    </div>
                </div>
                <div id="testList" class="test-list">
                    <!-- 示例测试项 -->
                    <div class="test-item">
                        <input type="checkbox" class="test-checkbox" checked>
                        <div class="test-info">
                            <div class="test-name">用户登录测试</div>
                            <div class="test-path">./tests/login.md</div>
                        </div>
                        <span class="test-status status-complete">完整</span>
                        <div class="test-actions">
                            <button class="btn-mini" title="生成步骤">✨</button>
                            <button class="btn-mini" title="执行测试">▶️</button>
                        </div>
                    </div>
                    <div class="test-item">
                        <input type="checkbox" class="test-checkbox">
                        <div class="test-info">
                            <div class="test-name">商品搜索测试</div>
                            <div class="test-path">./tests/search.md</div>
                        </div>
                        <span class="test-status status-missing-steps">缺少步骤</span>
                        <div class="test-actions">
                            <button class="btn-mini" title="生成步骤">✨</button>
                            <button class="btn-mini" title="执行测试">▶️</button>
                        </div>
                    </div>
                    <div class="test-item">
                        <input type="checkbox" class="test-checkbox">
                        <div class="test-info">
                            <div class="test-name">购物车功能测试</div>
                            <div class="test-path">./tests/cart.md</div>
                        </div>
                        <span class="test-status status-missing-result">缺少结果</span>
                        <div class="test-actions">
                            <button class="btn-mini" title="生成步骤">✨</button>
                            <button class="btn-mini" title="执行测试">▶️</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">
                    <h3>执行状态</h3>
                    <div class="status-controls">
                        <button id="clearStatusBtn" class="btn btn-small">
                            <span class="btn-icon">🗑️</span>
                            清空
                        </button>
                        <button id="exportStatusBtn" class="btn btn-small">
                            <span class="btn-icon">📤</span>
                            导出
                        </button>
                    </div>
                </div>
                <div id="statusArea" class="status-area">
                    <!-- 示例状态条目 -->
                    <div class="status-entry status-success">
                        <div class="status-timestamp">14:32:15</div>
                        <div class="status-message">用户登录测试执行成功</div>
                    </div>
                    <div class="status-entry status-info">
                        <div class="status-timestamp">14:31:45</div>
                        <div class="status-message">开始执行用户登录测试</div>
                    </div>
                    <div class="status-entry status-error">
                        <div class="status-timestamp">14:30:12</div>
                        <div class="status-message">商品搜索测试执行失败：找不到搜索按钮</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="config" class="tab-content">
            <div class="section">
                <div class="section-header">
                    <h3>🤖 AI服务配置</h3>
                    <div class="config-status" id="aiConfigStatus">
                        <span class="status-indicator status-valid"></span>
                        <span class="status-text">已配置</span>
                    </div>
                </div>
                <div class="config-grid">
                    <div class="form-group">
                        <label for="apiUrl">
                            <span class="label-icon">🌐</span>
                            API地址
                        </label>
                        <input type="text" id="apiUrl" placeholder="https://api.openai.com/v1" class="form-input" value="https://api.openai.com/v1">
                        <div class="input-help">输入OpenAI兼容的API地址</div>
                    </div>
                    <div class="form-group">
                        <label for="apiKey">
                            <span class="label-icon">🔑</span>
                            API密钥
                        </label>
                        <input type="password" id="apiKey" placeholder="sk-..." class="form-input" value="sk-1234567890abcdef">
                        <div class="input-help">您的API密钥将被安全存储</div>
                    </div>
                    <div class="form-group">
                        <label for="model">
                            <span class="label-icon">🧠</span>
                            AI模型
                        </label>
                        <select id="model" class="form-input">
                            <option value="gpt-3.5-turbo" selected>GPT-3.5 Turbo (推荐)</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="claude-3-opus">Claude 3 Opus</option>
                        </select>
                        <div class="input-help">选择适合的AI模型进行测试步骤生成</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">
                    <h3>📁 测试目录配置</h3>
                    <div class="config-status" id="dirConfigStatus">
                        <span class="status-indicator status-valid"></span>
                        <span class="status-text">已配置</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="testDirectory">
                        <span class="label-icon">📂</span>
                        测试目录路径
                    </label>
                    <div class="input-group">
                        <input type="text" id="testDirectory" placeholder="./test-docs" class="form-input" value="./tests">
                        <button id="browseBtn" class="btn btn-secondary">
                            <span class="btn-icon">📁</span>
                            浏览
                        </button>
                    </div>
                    <div class="input-help">选择包含测试文档(.md)和步骤文件(.yaml)的目录</div>
                </div>
                
                <div class="directory-info" id="directoryInfo">
                    <div class="info-card">
                        <h4>📊 目录统计</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-number" id="testDocsCount">12</span>
                                <span class="stat-label">测试文档</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="stepFilesCount">8</span>
                                <span class="stat-label">步骤文件</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="resultFilesCount">5</span>
                                <span class="stat-label">结果文件</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">
                    <h3>⚡ 操作中心</h3>
                </div>
                <div class="action-buttons">
                    <button id="saveConfigBtn" class="btn btn-primary">
                        <span class="btn-icon">💾</span>
                        保存配置
                    </button>
                    <button id="testConnectionBtn" class="btn btn-secondary">
                        <span class="btn-icon">🔗</span>
                        测试连接
                    </button>
                    <button id="validateConfigBtn" class="btn btn-secondary">
                        <span class="btn-icon">✅</span>
                        验证配置
                    </button>
                    <button id="resetConfigBtn" class="btn btn-danger">
                        <span class="btn-icon">🔄</span>
                        重置配置
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div id="notifications" class="notifications">
        <!-- 示例通知 -->
        <div class="notification notification-success">
            <div class="notification-icon">✅</div>
            <div class="notification-content">
                <div class="notification-title">配置保存成功</div>
                <div class="notification-message">AI服务配置和测试目录已成功保存</div>
            </div>
            <button class="notification-close">×</button>
        </div>
    </div>
    
    <script>
        // 简单的标签页切换功能
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                
                // 更新按钮状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                button.classList.add('active');
                
                // 更新内容显示
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabName).classList.add('active');
            });
        });
        
        // 通知关闭功能
        document.querySelectorAll('.notification-close').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.parentElement.remove();
            });
        });
        
        // 按钮点击效果
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('按钮点击:', e.target.textContent.trim());
            });
        });
    </script>
</body>
</html>
/**
 * 任务6测试脚本
 * 验证测试执行引擎的功能
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// 模拟测试步骤文档
const mockStepDocument = {
    name: "用户登录测试",
    description: "验证用户能够成功登录系统",
    target: "https://example.com",
    steps: [
        {
            goal: "访问登录页面",
            operation: "导航",
            content: "https://example.com/login"
        },
        {
            goal: "填写用户名",
            operation: "填写",
            content: "用户名字段: testuser"
        },
        {
            goal: "填写密码",
            operation: "填写",
            content: "密码字段: password123"
        },
        {
            goal: "点击登录按钮",
            operation: "点击",
            content: "登录按钮"
        },
        {
            goal: "验证登录成功",
            operation: "检查",
            content: "欢迎页面"
        }
    ]
};

// 模拟测试执行结果
const mockExecutionResult = {
    testDocument: mockStepDocument,
    status: 'completed',
    startTime: new Date('2025-01-20T10:00:00Z'),
    endTime: new Date('2025-01-20T10:02:30Z'),
    duration: 150000, // 2.5分钟
    stepResults: [
        {
            step: mockStepDocument.steps[0],
            status: 'completed',
            startTime: new Date('2025-01-20T10:00:00Z'),
            endTime: new Date('2025-01-20T10:00:15Z'),
            duration: 15000,
            result: {
                content: [{ type: 'text', text: '页面导航成功' }],
                isError: false
            }
        },
        {
            step: mockStepDocument.steps[1],
            status: 'completed',
            startTime: new Date('2025-01-20T10:00:15Z'),
            endTime: new Date('2025-01-20T10:00:25Z'),
            duration: 10000,
            result: {
                content: [{ type: 'text', text: '用户名填写成功' }],
                isError: false
            }
        },
        {
            step: mockStepDocument.steps[2],
            status: 'completed',
            startTime: new Date('2025-01-20T10:00:25Z'),
            endTime: new Date('2025-01-20T10:00:35Z'),
            duration: 10000,
            result: {
                content: [{ type: 'text', text: '密码填写成功' }],
                isError: false
            }
        },
        {
            step: mockStepDocument.steps[3],
            status: 'completed',
            startTime: new Date('2025-01-20T10:00:35Z'),
            endTime: new Date('2025-01-20T10:01:00Z'),
            duration: 25000,
            result: {
                content: [{ type: 'text', text: '登录按钮点击成功' }],
                isError: false
            },
            screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        },
        {
            step: mockStepDocument.steps[4],
            status: 'completed',
            startTime: new Date('2025-01-20T10:01:00Z'),
            endTime: new Date('2025-01-20T10:02:30Z'),
            duration: 90000,
            result: {
                content: [{ type: 'text', text: '登录验证成功' }],
                isError: false
            }
        }
    ],
    totalSteps: 5,
    completedSteps: 5,
    failedSteps: 0
};

/**
 * 测试YAML步骤文档解析
 */
function testStepDocumentParsing() {
    console.log('=== 测试YAML步骤文档解析 ===');
    
    try {
        // 生成YAML内容
        const yamlContent = yaml.dump(mockStepDocument);
        console.log('生成的YAML内容:');
        console.log(yamlContent);
        console.log('');
        
        // 解析YAML内容
        const parsed = yaml.load(yamlContent);
        console.log('解析后的对象:');
        console.log(JSON.stringify(parsed, null, 2));
        console.log('');
        
        // 验证必需字段
        const requiredFields = ['name', 'description', 'steps'];
        const missingFields = requiredFields.filter(field => !parsed[field]);
        
        if (missingFields.length > 0) {
            throw new Error(`缺少必需字段: ${missingFields.join(', ')}`);
        }
        
        // 验证步骤格式
        if (!Array.isArray(parsed.steps) || parsed.steps.length === 0) {
            throw new Error('步骤列表为空或格式不正确');
        }
        
        for (let i = 0; i < parsed.steps.length; i++) {
            const step = parsed.steps[i];
            const stepRequiredFields = ['goal', 'operation', 'content'];
            const stepMissingFields = stepRequiredFields.filter(field => !step[field]);
            
            if (stepMissingFields.length > 0) {
                throw new Error(`步骤 ${i + 1} 缺少必需字段: ${stepMissingFields.join(', ')}`);
            }
        }
        
        console.log('✅ YAML步骤文档解析测试通过');
        console.log(`解析出 ${parsed.steps.length} 个测试步骤`);
        return true;
        
    } catch (error) {
        console.error('❌ YAML步骤文档解析测试失败:', error.message);
        return false;
    }
}

/**
 * 测试步骤到MCP工具调用的转换
 */
function testStepToToolCallConversion() {
    console.log('\n=== 测试步骤到MCP工具调用转换 ===');
    
    try {
        const testCases = [
            {
                step: { goal: "访问页面", operation: "导航", content: "https://example.com" },
                expected: { name: 'navigate', arguments: { url: 'https://example.com' } }
            },
            {
                step: { goal: "点击按钮", operation: "点击", content: "登录按钮" },
                expected: { name: 'click', arguments: { selector: '登录按钮' } }
            },
            {
                step: { goal: "填写用户名", operation: "填写", content: "用户名字段: testuser" },
                expected: { name: 'fill', arguments: { selector: '用户名字段', text: 'testuser' } }
            },
            {
                step: { goal: "检查元素", operation: "检查", content: "欢迎消息" },
                expected: { name: 'expect', arguments: { selector: '欢迎消息', assertion: 'toBeVisible' } }
            },
            {
                step: { goal: "等待3秒", operation: "等待", content: "3秒" },
                expected: { name: 'wait', arguments: { timeout: 3000 } }
            },
            {
                step: { goal: "截图", operation: "截图", content: "login.png" },
                expected: { name: 'screenshot', arguments: { path: 'login.png' } }
            }
        ];

        let passedTests = 0;
        
        for (let i = 0; i < testCases.length; i++) {
            const testCase = testCases[i];
            console.log(`测试用例 ${i + 1}: ${testCase.step.operation} - ${testCase.step.goal}`);
            
            try {
                const result = convertStepToToolCall(testCase.step);
                
                if (JSON.stringify(result) === JSON.stringify(testCase.expected)) {
                    console.log('✅ 转换正确');
                    passedTests++;
                } else {
                    console.log('❌ 转换错误');
                    console.log('期望:', JSON.stringify(testCase.expected));
                    console.log('实际:', JSON.stringify(result));
                }
            } catch (error) {
                console.log('❌ 转换失败:', error.message);
            }
            
            console.log('');
        }
        
        console.log(`步骤转换测试结果: ${passedTests}/${testCases.length} 通过`);
        return passedTests === testCases.length;
        
    } catch (error) {
        console.error('❌ 步骤转换测试失败:', error.message);
        return false;
    }
}

/**
 * 模拟步骤到工具调用转换函数
 */
function convertStepToToolCall(step) {
    const operation = step.operation.toLowerCase();
    
    switch (operation) {
        case '导航':
            return {
                name: 'navigate',
                arguments: { url: step.content }
            };
            
        case '点击':
            return {
                name: 'click',
                arguments: { selector: step.content }
            };
            
        case '填写':
            const fillMatch = step.content.match(/^(.+?):\s*(.+)$/);
            if (fillMatch) {
                return {
                    name: 'fill',
                    arguments: {
                        selector: fillMatch[1].trim(),
                        text: fillMatch[2].trim()
                    }
                };
            } else {
                throw new Error(`填写步骤格式不正确: ${step.content}`);
            }
            
        case '检查':
            return {
                name: 'expect',
                arguments: { 
                    selector: step.content,
                    assertion: 'toBeVisible'
                }
            };
            
        case '等待':
            const waitTimeMatch = step.content.match(/(\d+)\s*秒?/);
            if (waitTimeMatch) {
                return {
                    name: 'wait',
                    arguments: { timeout: parseInt(waitTimeMatch[1]) * 1000 }
                };
            } else {
                return {
                    name: 'waitForSelector',
                    arguments: { selector: step.content }
                };
            }
            
        case '截图':
            return {
                name: 'screenshot',
                arguments: { path: step.content || 'screenshot.png' }
            };
            
        default:
            throw new Error(`不支持的操作类型: ${step.operation}`);
    }
}

/**
 * 测试Markdown报告生成
 */
function testMarkdownReportGeneration() {
    console.log('\n=== 测试Markdown报告生成 ===');
    
    try {
        const markdownReport = generateMarkdownReport(mockExecutionResult);
        
        console.log('生成的Markdown报告:');
        console.log('---');
        console.log(markdownReport);
        console.log('---');
        
        // 验证报告内容
        const requiredSections = [
            '# 测试报告:',
            '## 执行统计',
            '## 步骤执行详情',
            '## 测试总结'
        ];
        
        let missingSection = false;
        for (const section of requiredSections) {
            if (!markdownReport.includes(section)) {
                console.error(`❌ 缺少必需章节: ${section}`);
                missingSection = true;
            }
        }
        
        if (!missingSection) {
            console.log('✅ Markdown报告生成测试通过');
            return true;
        } else {
            return false;
        }
        
    } catch (error) {
        console.error('❌ Markdown报告生成测试失败:', error.message);
        return false;
    }
}

/**
 * 模拟Markdown报告生成函数
 */
function generateMarkdownReport(result) {
    const lines = [];

    // 标题和基本信息
    lines.push(`# 测试报告: ${result.testDocument.name}`);
    lines.push('');
    lines.push(`**测试描述**: ${result.testDocument.description}`);
    lines.push(`**目标地址**: ${result.testDocument.target || 'N/A'}`);
    lines.push(`**执行状态**: ${getStatusEmoji(result.status)} ${getStatusText(result.status)}`);
    lines.push('');

    // 执行统计
    lines.push('## 执行统计');
    lines.push('');
    lines.push(`- **总步骤数**: ${result.totalSteps}`);
    lines.push(`- **完成步骤**: ${result.completedSteps}`);
    lines.push(`- **失败步骤**: ${result.failedSteps}`);
    lines.push(`- **成功率**: ${result.totalSteps > 0 ? ((result.completedSteps / result.totalSteps) * 100).toFixed(1) : 0}%`);
    lines.push(`- **开始时间**: ${formatDateTime(result.startTime)}`);
    if (result.endTime) {
        lines.push(`- **结束时间**: ${formatDateTime(result.endTime)}`);
    }
    if (result.duration) {
        lines.push(`- **执行时长**: ${formatDuration(result.duration)}`);
    }
    lines.push('');

    // 步骤详情
    lines.push('## 步骤执行详情');
    lines.push('');

    result.stepResults.forEach((stepResult, index) => {
        lines.push(`### 步骤 ${index + 1}: ${stepResult.step.goal}`);
        lines.push('');
        lines.push(`**操作**: ${stepResult.step.operation}`);
        lines.push(`**内容**: ${stepResult.step.content}`);
        lines.push(`**状态**: ${getStatusEmoji(stepResult.status)} ${getStepStatusText(stepResult.status)}`);
        
        if (stepResult.duration) {
            lines.push(`**执行时长**: ${formatDuration(stepResult.duration)}`);
        }

        if (stepResult.screenshot) {
            lines.push('');
            lines.push(`![步骤${index + 1}截图](${stepResult.screenshot})`);
        }

        lines.push('');
        lines.push('---');
        lines.push('');
    });

    // 总结
    lines.push('## 测试总结');
    lines.push('');
    if (result.status === 'completed') {
        lines.push('✅ **测试执行成功完成**');
    } else if (result.status === 'failed') {
        lines.push('❌ **测试执行失败**');
    }

    lines.push('');
    lines.push(`*报告生成时间: ${formatDateTime(new Date())}*`);

    return lines.join('\n');
}

// 辅助函数
function getStatusEmoji(status) {
    switch (status) {
        case 'completed': return '✅';
        case 'failed': return '❌';
        case 'cancelled': return '⚠️';
        case 'running': return '🔄';
        default: return '⏸️';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'pending': return '等待中';
        case 'running': return '执行中';
        case 'completed': return '已完成';
        case 'failed': return '执行失败';
        case 'cancelled': return '已取消';
        default: return '未知状态';
    }
}

function getStepStatusText(status) {
    switch (status) {
        case 'pending': return '等待中';
        case 'running': return '执行中';
        case 'completed': return '已完成';
        case 'failed': return '执行失败';
        case 'skipped': return '已跳过';
        default: return '未知状态';
    }
}

function formatDateTime(date) {
    return date.toLocaleString('zh-CN');
}

function formatDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`;
    } else {
        return `${seconds}秒`;
    }
}

/**
 * 主测试函数
 */
function runTests() {
    console.log('开始任务6测试执行引擎功能测试...\n');
    
    const tests = [
        { name: 'YAML步骤文档解析', fn: testStepDocumentParsing },
        { name: '步骤到MCP工具调用转换', fn: testStepToToolCallConversion },
        { name: 'Markdown报告生成', fn: testMarkdownReportGeneration }
    ];
    
    let passedTests = 0;
    const totalTests = tests.length;
    
    for (const test of tests) {
        if (test.fn()) {
            passedTests++;
        }
    }
    
    console.log('\n=== 测试结果汇总 ===');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！测试执行引擎功能正常');
    } else {
        console.log('⚠️  部分测试失败，请检查相关功能');
    }
}

// 运行测试
if (require.main === module) {
    runTests();
}

module.exports = {
    testStepDocumentParsing,
    testStepToToolCallConversion,
    testMarkdownReportGeneration,
    runTests
};

#!/usr/bin/env node

/**
 * 测试样式加载脚本
 * 验证WebView资源文件是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 检查样式文件加载配置...\n');

let allPassed = true;

// 检查dist目录中的资源文件
console.log('1. 检查编译后的资源文件:');
const distCssPath = 'dist/ui/assets/main.css';
const distJsPath = 'dist/ui/assets/main.js';

if (fs.existsSync(distCssPath)) {
    const cssSize = fs.statSync(distCssPath).size;
    console.log(`✅ CSS文件存在: ${distCssPath} (${(cssSize/1024).toFixed(1)}KB)`);
} else {
    console.log(`❌ CSS文件不存在: ${distCssPath}`);
    allPassed = false;
}

if (fs.existsSync(distJsPath)) {
    const jsSize = fs.statSync(distJsPath).size;
    console.log(`✅ JS文件存在: ${distJsPath} (${(jsSize/1024).toFixed(1)}KB)`);
} else {
    console.log(`❌ JS文件不存在: ${distJsPath}`);
    allPassed = false;
}

// 检查CSS文件内容
console.log('\n2. 检查CSS样式内容:');
if (fs.existsSync(distCssPath)) {
    const cssContent = fs.readFileSync(distCssPath, 'utf8');
    
    const checks = [
        { name: '现代设计变量', pattern: '--border-radius:' },
        { name: 'VS Code主题变量', pattern: '--vscode-foreground' },
        { name: '渐变样式', pattern: 'linear-gradient' },
        { name: '动画效果', pattern: '@keyframes' },
        { name: '响应式设计', pattern: '@media' },
        { name: '现代化按钮', pattern: '.btn-primary' },
        { name: '测试列表样式', pattern: '.test-item' },
        { name: '通知系统', pattern: '.notification' }
    ];
    
    checks.forEach(check => {
        if (cssContent.includes(check.pattern)) {
            console.log(`✅ ${check.name}: 已包含`);
        } else {
            console.log(`❌ ${check.name}: 未找到`);
            allPassed = false;
        }
    });
}

// 检查JS文件内容
console.log('\n3. 检查JavaScript功能:');
if (fs.existsSync(distJsPath)) {
    const jsContent = fs.readFileSync(distJsPath, 'utf8');
    
    const checks = [
        { name: 'VS Code API', pattern: 'acquireVsCodeApi' },
        { name: '标签页切换', pattern: 'switchTab' },
        { name: '通知系统', pattern: 'showNotification' },
        { name: '加载遮罩', pattern: 'showLoading' },
        { name: '状态管理', pattern: 'selectedTests' },
        { name: '事件绑定', pattern: 'addEventListener' }
    ];
    
    checks.forEach(check => {
        if (jsContent.includes(check.pattern)) {
            console.log(`✅ ${check.name}: 已包含`);
        } else {
            console.log(`❌ ${check.name}: 未找到`);
            allPassed = false;
        }
    });
}

// 检查WebView配置
console.log('\n4. 检查WebView配置:');
const mainPanelPath = 'src/ui/panels/mainPanel.ts';
if (fs.existsSync(mainPanelPath)) {
    const panelContent = fs.readFileSync(mainPanelPath, 'utf8');
    
    if (panelContent.includes('dist/ui/assets')) {
        console.log('✅ WebView资源路径: 指向dist目录');
    } else {
        console.log('❌ WebView资源路径: 未指向dist目录');
        allPassed = false;
    }
    
    if (panelContent.includes('localResourceRoots')) {
        console.log('✅ 本地资源根目录: 已配置');
    } else {
        console.log('❌ 本地资源根目录: 未配置');
        allPassed = false;
    }
}

// 检查构建配置
console.log('\n5. 检查构建配置:');
const esbuildPath = 'esbuild.js';
if (fs.existsSync(esbuildPath)) {
    const buildContent = fs.readFileSync(esbuildPath, 'utf8');
    
    if (buildContent.includes('copyAssetsPlugin')) {
        console.log('✅ 资源复制插件: 已配置');
    } else {
        console.log('❌ 资源复制插件: 未配置');
        allPassed = false;
    }
}

console.log('\n📊 检查结果:');
if (allPassed) {
    console.log('🎉 所有检查项都通过！样式应该能正确加载了。');
    console.log('\n🚀 现在可以重新安装插件测试:');
    console.log('   1. 重新启动VS Code调试 (F5)');
    console.log('   2. 或者重新安装插件: code --install-extension ai-test-0.0.1.vsix');
    console.log('\n💡 如果样式仍然没有加载，请尝试:');
    console.log('   - 重启VS Code');
    console.log('   - 清除VS Code缓存');
    console.log('   - 检查开发者工具中的网络请求');
} else {
    console.log('❌ 部分检查项未通过，需要修复配置。');
}
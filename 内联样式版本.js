#!/usr/bin/env node

/**
 * 创建内联样式版本的WebView
 * 如果外部CSS文件加载有问题，可以使用这个版本
 */

const fs = require('fs');

console.log('📝 创建内联样式版本...');

// 读取CSS文件内容
const cssPath = 'src/ui/assets/main.css';
const jsPath = 'src/ui/assets/main.js';

if (!fs.existsSync(cssPath) || !fs.existsSync(jsPath)) {
    console.log('❌ 找不到CSS或JS文件');
    process.exit(1);
}

const cssContent = fs.readFileSync(cssPath, 'utf8');
const jsContent = fs.readFileSync(jsPath, 'utf8');

// 创建内联版本的HTML生成函数
const inlineVersion = `
    private _getHtmlForWebview(webview: vscode.Webview) {
        const nonce = this.getNonce();

        return \`<!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-\${nonce}'; img-src data:;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI自动化测试</title>
                <style>
                    ${cssContent.replace(/`/g, '\\`')}
                </style>
            </head>
            <body class="vscode-body">
                <div class="container">
                    <div class="header">
                        <h1 class="title">AI自动化测试</h1>
                        <div class="tabs">
                            <button class="tab-button active" data-tab="tests">
                                <span class="tab-icon">🧪</span>
                                <span class="tab-text">测试</span>
                            </button>
                            <button class="tab-button" data-tab="config">
                                <span class="tab-icon">⚙️</span>
                                <span class="tab-text">配置</span>
                            </button>
                        </div>
                    </div>
                    
                    <div id="tests" class="tab-content active">
                        <div class="section">
                            <div class="section-header">
                                <h3>测试文档管理</h3>
                                <div class="controls">
                                    <button id="scanBtn" class="btn btn-primary">
                                        <span class="btn-icon">🔍</span>
                                        扫描文档
                                    </button>
                                    <button id="selectAllBtn" class="btn btn-secondary">
                                        <span class="btn-icon">☑️</span>
                                        全选
                                    </button>
                                    <button id="executeBtn" class="btn btn-success" disabled>
                                        <span class="btn-icon">🚀</span>
                                        执行测试
                                    </button>
                                    <button id="generateBtn" class="btn btn-secondary">
                                        <span class="btn-icon">✨</span>
                                        生成步骤
                                    </button>
                                </div>
                            </div>
                            <div id="testList" class="test-list">
                                <div class="placeholder">
                                    <div class="placeholder-icon">🔍</div>
                                    <p>点击"扫描文档"开始发现测试文件</p>
                                    <small>支持 .md 测试文档和 .yaml 步骤文件</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section">
                            <div class="section-header">
                                <h3>执行状态</h3>
                                <div class="status-controls">
                                    <button id="clearStatusBtn" class="btn btn-small">
                                        <span class="btn-icon">🗑️</span>
                                        清空
                                    </button>
                                    <button id="exportStatusBtn" class="btn btn-small">
                                        <span class="btn-icon">📤</span>
                                        导出
                                    </button>
                                </div>
                            </div>
                            <div id="statusArea" class="status-area">
                                <div class="placeholder">
                                    <div class="placeholder-icon">📊</div>
                                    <p>暂无执行状态</p>
                                    <small>测试执行日志将在这里显示</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="config" class="tab-content">
                        <div class="section">
                            <div class="section-header">
                                <h3>🤖 AI服务配置</h3>
                                <div class="config-status" id="aiConfigStatus">
                                    <span class="status-indicator status-unknown"></span>
                                    <span class="status-text">未配置</span>
                                </div>
                            </div>
                            <div class="config-grid">
                                <div class="form-group">
                                    <label for="apiUrl">
                                        <span class="label-icon">🌐</span>
                                        API地址
                                    </label>
                                    <input type="text" id="apiUrl" placeholder="https://api.openai.com/v1" class="form-input">
                                    <div class="input-help">输入OpenAI兼容的API地址</div>
                                </div>
                                <div class="form-group">
                                    <label for="apiKey">
                                        <span class="label-icon">🔑</span>
                                        API密钥
                                    </label>
                                    <input type="password" id="apiKey" placeholder="sk-..." class="form-input">
                                    <div class="input-help">您的API密钥将被安全存储</div>
                                </div>
                                <div class="form-group">
                                    <label for="model">
                                        <span class="label-icon">🧠</span>
                                        AI模型
                                    </label>
                                    <select id="model" class="form-input">
                                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo (推荐)</option>
                                        <option value="gpt-4">GPT-4</option>
                                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                        <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                                        <option value="claude-3-opus">Claude 3 Opus</option>
                                    </select>
                                    <div class="input-help">选择适合的AI模型进行测试步骤生成</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section">
                            <div class="section-header">
                                <h3>📁 测试目录配置</h3>
                                <div class="config-status" id="dirConfigStatus">
                                    <span class="status-indicator status-unknown"></span>
                                    <span class="status-text">未配置</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="testDirectory">
                                    <span class="label-icon">📂</span>
                                    测试目录路径
                                </label>
                                <div class="input-group">
                                    <input type="text" id="testDirectory" placeholder="./test-docs" class="form-input">
                                    <button id="browseBtn" class="btn btn-secondary">
                                        <span class="btn-icon">📁</span>
                                        浏览
                                    </button>
                                </div>
                                <div class="input-help">选择包含测试文档(.md)和步骤文件(.yaml)的目录</div>
                            </div>
                            
                            <div class="directory-info" id="directoryInfo" style="display: none;">
                                <div class="info-card">
                                    <h4>📊 目录统计</h4>
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <span class="stat-number" id="testDocsCount">0</span>
                                            <span class="stat-label">测试文档</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number" id="stepFilesCount">0</span>
                                            <span class="stat-label">步骤文件</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number" id="resultFilesCount">0</span>
                                            <span class="stat-label">结果文件</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section">
                            <div class="section-header">
                                <h3>⚡ 操作中心</h3>
                            </div>
                            <div class="action-buttons">
                                <button id="saveConfigBtn" class="btn btn-primary">
                                    <span class="btn-icon">💾</span>
                                    保存配置
                                </button>
                                <button id="testConnectionBtn" class="btn btn-secondary">
                                    <span class="btn-icon">🔗</span>
                                    测试连接
                                </button>
                                <button id="validateConfigBtn" class="btn btn-secondary">
                                    <span class="btn-icon">✅</span>
                                    验证配置
                                </button>
                                <button id="resetConfigBtn" class="btn btn-danger">
                                    <span class="btn-icon">🔄</span>
                                    重置配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 通知容器 -->
                <div id="notifications" class="notifications"></div>
                
                <!-- 加载遮罩 -->
                <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">处理中...</div>
                </div>
                
                <script nonce="\${nonce}">
                    ${jsContent.replace(/`/g, '\\`')}
                </script>
            </body>
            </html>\`;
    }
`;

console.log('✅ 内联样式版本已生成');
console.log('\n📋 使用方法:');
console.log('1. 复制上面的函数代码');
console.log('2. 替换 src/ui/panels/mainPanel.ts 中的 _getHtmlForWebview 方法');
console.log('3. 重新编译: npm run compile');
console.log('4. 重新测试插件');

// 将内联版本保存到文件
fs.writeFileSync('内联样式版本.txt', inlineVersion);
console.log('\n💾 内联版本已保存到: 内联样式版本.txt');
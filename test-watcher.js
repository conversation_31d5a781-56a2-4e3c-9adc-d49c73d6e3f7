// 测试文件系统监听功能
const fs = require('fs');
const path = require('path');

async function testWatcher() {
    console.log('测试文件系统监听功能...');
    
    const testDir = path.join(__dirname, 'test-docs');
    const testFile = path.join(testDir, 'temp-test.md');
    
    console.log('\n1. 创建测试文件...');
    fs.writeFileSync(testFile, '# 临时测试文件\n\n这是一个测试文件。');
    console.log(`创建文件: ${testFile}`);
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n2. 修改测试文件...');
    fs.appendFileSync(testFile, '\n\n## 新增内容\n\n这是修改后的内容。');
    console.log(`修改文件: ${testFile}`);
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n3. 删除测试文件...');
    fs.unlinkSync(testFile);
    console.log(`删除文件: ${testFile}`);
    
    console.log('\n✅ 文件系统操作测试完成！');
    console.log('注意: 实际的监听功能需要在 VS Code 插件环境中测试。');
}

testWatcher().catch(console.error);
import * as fs from 'fs';
import * as path from 'path';
import { TestExecutionResult, StepExecutionResult, TestExecutionStatus, StepExecutionStatus } from './testExecutor';

/**
 * 测试报告配置接口
 */
export interface ReportConfig {
    includeScreenshots: boolean;
    includeStepDetails: boolean;
    includeTimestamps: boolean;
    includeErrorDetails: boolean;
    outputFormat: 'markdown' | 'html' | 'json';
}

/**
 * 报告生成结果接口
 */
export interface ReportGenerationResult {
    success: boolean;
    filePath?: string;
    content?: string;
    error?: string;
}

/**
 * 测试结果生成器类
 * 负责生成Markdown格式的测试结果报告，收集截图和错误信息
 */
export class ResultGenerator {
    private static instance: ResultGenerator;

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): ResultGenerator {
        if (!ResultGenerator.instance) {
            ResultGenerator.instance = new ResultGenerator();
        }
        return ResultGenerator.instance;
    }

    /**
     * 生成测试结果报告
     * @param executionResult 测试执行结果
     * @param outputPath 输出路径
     * @param config 报告配置
     * @returns 报告生成结果
     */
    public async generateReport(
        executionResult: TestExecutionResult,
        outputPath: string,
        config: ReportConfig = this.getDefaultConfig()
    ): Promise<ReportGenerationResult> {
        try {
            console.log(`生成测试报告: ${outputPath}`);

            let content: string;
            let fileName: string;

            switch (config.outputFormat) {
                case 'markdown':
                    content = this.generateMarkdownReport(executionResult, config);
                    fileName = path.basename(outputPath, path.extname(outputPath)) + '.md';
                    break;
                case 'html':
                    content = this.generateHtmlReport(executionResult, config);
                    fileName = path.basename(outputPath, path.extname(outputPath)) + '.html';
                    break;
                case 'json':
                    content = this.generateJsonReport(executionResult);
                    fileName = path.basename(outputPath, path.extname(outputPath)) + '.json';
                    break;
                default:
                    throw new Error(`不支持的输出格式: ${config.outputFormat}`);
            }

            const fullPath = path.join(path.dirname(outputPath), fileName);

            // 确保目录存在
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // 保存报告文件
            fs.writeFileSync(fullPath, content, 'utf-8');

            console.log(`测试报告生成成功: ${fullPath}`);
            return {
                success: true,
                filePath: fullPath,
                content
            };

        } catch (error) {
            console.error('生成测试报告失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误'
            };
        }
    }

    /**
     * 生成Markdown格式报告
     * @param result 测试执行结果
     * @param config 报告配置
     * @returns Markdown内容
     */
    private generateMarkdownReport(result: TestExecutionResult, config: ReportConfig): string {
        const lines: string[] = [];

        // 标题和基本信息
        lines.push(`# 测试报告: ${result.testDocument.name}`);
        lines.push('');
        lines.push(`**测试描述**: ${result.testDocument.description}`);
        lines.push(`**目标地址**: ${result.testDocument.target || 'N/A'}`);
        lines.push(`**执行状态**: ${this.getStatusEmoji(result.status)} ${this.getStatusText(result.status)}`);
        lines.push('');

        // 执行统计
        lines.push('## 执行统计');
        lines.push('');
        lines.push(`- **总步骤数**: ${result.totalSteps}`);
        lines.push(`- **完成步骤**: ${result.completedSteps}`);
        lines.push(`- **失败步骤**: ${result.failedSteps}`);
        lines.push(`- **成功率**: ${result.totalSteps > 0 ? ((result.completedSteps / result.totalSteps) * 100).toFixed(1) : 0}%`);
        
        if (config.includeTimestamps) {
            lines.push(`- **开始时间**: ${this.formatDateTime(result.startTime)}`);
            if (result.endTime) {
                lines.push(`- **结束时间**: ${this.formatDateTime(result.endTime)}`);
            }
            if (result.duration) {
                lines.push(`- **执行时长**: ${this.formatDuration(result.duration)}`);
            }
        }
        lines.push('');

        // 错误信息（如果有）
        if (result.error && config.includeErrorDetails) {
            lines.push('## 执行错误');
            lines.push('');
            lines.push('```');
            lines.push(result.error);
            lines.push('```');
            lines.push('');
        }

        // 步骤详情
        if (config.includeStepDetails && result.stepResults.length > 0) {
            lines.push('## 步骤执行详情');
            lines.push('');

            result.stepResults.forEach((stepResult, index) => {
                lines.push(`### 步骤 ${index + 1}: ${stepResult.step.goal}`);
                lines.push('');
                lines.push(`**操作**: ${stepResult.step.operation}`);
                lines.push(`**内容**: ${stepResult.step.content}`);
                lines.push(`**状态**: ${this.getStatusEmoji(stepResult.status)} ${this.getStepStatusText(stepResult.status)}`);
                
                if (config.includeTimestamps && stepResult.duration) {
                    lines.push(`**执行时长**: ${this.formatDuration(stepResult.duration)}`);
                }

                // 错误信息
                if (stepResult.error && config.includeErrorDetails) {
                    lines.push('');
                    lines.push('**错误信息**:');
                    lines.push('```');
                    lines.push(stepResult.error);
                    lines.push('```');
                }

                // 截图
                if (stepResult.screenshot && config.includeScreenshots) {
                    lines.push('');
                    if (stepResult.screenshot.startsWith('data:')) {
                        // Base64图像数据
                        lines.push(`![步骤${index + 1}截图](${stepResult.screenshot})`);
                    } else {
                        // 文件路径
                        lines.push(`![步骤${index + 1}截图](${stepResult.screenshot})`);
                    }
                }

                lines.push('');
                lines.push('---');
                lines.push('');
            });
        }

        // 总结
        lines.push('## 测试总结');
        lines.push('');
        if (result.status === TestExecutionStatus.COMPLETED) {
            lines.push('✅ **测试执行成功完成**');
        } else if (result.status === TestExecutionStatus.FAILED) {
            lines.push('❌ **测试执行失败**');
            if (result.failedSteps > 0) {
                lines.push(`- 共有 ${result.failedSteps} 个步骤执行失败`);
            }
        } else if (result.status === TestExecutionStatus.CANCELLED) {
            lines.push('⚠️ **测试执行被取消**');
        }

        lines.push('');
        lines.push(`*报告生成时间: ${this.formatDateTime(new Date())}*`);

        return lines.join('\n');
    }

    /**
     * 生成HTML格式报告
     * @param result 测试执行结果
     * @param config 报告配置
     * @returns HTML内容
     */
    private generateHtmlReport(result: TestExecutionResult, config: ReportConfig): string {
        const markdownContent = this.generateMarkdownReport(result, config);
        
        // 简单的Markdown到HTML转换
        let htmlContent = markdownContent
            .replace(/^# (.+)$/gm, '<h1>$1</h1>')
            .replace(/^## (.+)$/gm, '<h2>$1</h2>')
            .replace(/^### (.+)$/gm, '<h3>$1</h3>')
            .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.+?)\*/g, '<em>$1</em>')
            .replace(/^- (.+)$/gm, '<li>$1</li>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/\n/g, '<br>\n');

        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告: ${result.testDocument.name}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .status-success { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-cancelled { color: #ffc107; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 4px; }
        img { max-width: 100%; height: auto; }
        hr { margin: 20px 0; }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
    }

    /**
     * 生成JSON格式报告
     * @param result 测试执行结果
     * @returns JSON内容
     */
    private generateJsonReport(result: TestExecutionResult): string {
        return JSON.stringify(result, null, 2);
    }

    /**
     * 获取状态表情符号
     * @param status 状态
     * @returns 表情符号
     */
    private getStatusEmoji(status: TestExecutionStatus | StepExecutionStatus): string {
        switch (status) {
            case TestExecutionStatus.COMPLETED:
            case StepExecutionStatus.COMPLETED:
                return '✅';
            case TestExecutionStatus.FAILED:
            case StepExecutionStatus.FAILED:
                return '❌';
            case TestExecutionStatus.CANCELLED:
                return '⚠️';
            case TestExecutionStatus.RUNNING:
            case StepExecutionStatus.RUNNING:
                return '🔄';
            case StepExecutionStatus.SKIPPED:
                return '⏭️';
            default:
                return '⏸️';
        }
    }

    /**
     * 获取状态文本
     * @param status 测试执行状态
     * @returns 状态文本
     */
    private getStatusText(status: TestExecutionStatus): string {
        switch (status) {
            case TestExecutionStatus.PENDING:
                return '等待中';
            case TestExecutionStatus.RUNNING:
                return '执行中';
            case TestExecutionStatus.COMPLETED:
                return '已完成';
            case TestExecutionStatus.FAILED:
                return '执行失败';
            case TestExecutionStatus.CANCELLED:
                return '已取消';
            default:
                return '未知状态';
        }
    }

    /**
     * 获取步骤状态文本
     * @param status 步骤执行状态
     * @returns 状态文本
     */
    private getStepStatusText(status: StepExecutionStatus): string {
        switch (status) {
            case StepExecutionStatus.PENDING:
                return '等待中';
            case StepExecutionStatus.RUNNING:
                return '执行中';
            case StepExecutionStatus.COMPLETED:
                return '已完成';
            case StepExecutionStatus.FAILED:
                return '执行失败';
            case StepExecutionStatus.SKIPPED:
                return '已跳过';
            default:
                return '未知状态';
        }
    }

    /**
     * 格式化日期时间
     * @param date 日期对象
     * @returns 格式化的日期时间字符串
     */
    private formatDateTime(date: Date): string {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 格式化持续时间
     * @param duration 持续时间（毫秒）
     * @returns 格式化的持续时间字符串
     */
    private formatDuration(duration: number): string {
        const seconds = Math.floor(duration / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
        } else if (minutes > 0) {
            return `${minutes}分钟${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 获取默认报告配置
     * @returns 默认配置
     */
    private getDefaultConfig(): ReportConfig {
        return {
            includeScreenshots: true,
            includeStepDetails: true,
            includeTimestamps: true,
            includeErrorDetails: true,
            outputFormat: 'markdown'
        };
    }

    /**
     * 保存截图文件
     * @param screenshotData Base64截图数据
     * @param outputDir 输出目录
     * @param fileName 文件名
     * @returns 保存的文件路径
     */
    public async saveScreenshot(
        screenshotData: string,
        outputDir: string,
        fileName: string
    ): Promise<string> {
        try {
            // 确保目录存在
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            const filePath = path.join(outputDir, fileName);

            if (screenshotData.startsWith('data:image/')) {
                // Base64数据，需要解码
                const base64Data = screenshotData.split(',')[1];
                const buffer = Buffer.from(base64Data, 'base64');
                fs.writeFileSync(filePath, buffer);
            } else {
                // 假设是已经是文件路径或其他格式
                fs.writeFileSync(filePath, screenshotData);
            }

            return filePath;

        } catch (error) {
            throw new Error(`保存截图失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 生成测试报告文件名
     * @param testName 测试名称
     * @param timestamp 时间戳
     * @returns 文件名
     */
    public generateReportFileName(testName: string, timestamp: Date = new Date()): string {
        const dateStr = timestamp.toISOString().slice(0, 19).replace(/[:.]/g, '-');
        const safeName = testName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_');
        return `${safeName}_${dateStr}`;
    }
}

import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { MCPClient, MCPToolCall, MCPToolResult } from './mcpClient';
import { AIServiceClient } from './aiService';

/**
 * 测试步骤接口
 */
export interface TestStep {
    goal: string;
    operation: string;
    content: string;
}

/**
 * 测试步骤文档接口
 */
export interface TestStepDocument {
    name: string;
    description: string;
    target: string;
    steps: TestStep[];
}

/**
 * 测试执行状态枚举
 */
export enum TestExecutionStatus {
    PENDING = 'pending',
    RUNNING = 'running',
    COMPLETED = 'completed',
    FAILED = 'failed',
    CANCELLED = 'cancelled'
}

/**
 * 步骤执行状态枚举
 */
export enum StepExecutionStatus {
    PENDING = 'pending',
    RUNNING = 'running',
    COMPLETED = 'completed',
    FAILED = 'failed',
    SKIPPED = 'skipped'
}

/**
 * 步骤执行结果接口
 */
export interface StepExecutionResult {
    step: TestStep;
    status: StepExecutionStatus;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    result?: MCPToolResult;
    error?: string;
    screenshot?: string;
}

/**
 * 测试执行结果接口
 */
export interface TestExecutionResult {
    testDocument: TestStepDocument;
    status: TestExecutionStatus;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    stepResults: StepExecutionResult[];
    totalSteps: number;
    completedSteps: number;
    failedSteps: number;
    error?: string;
}

/**
 * 测试执行配置接口
 */
export interface TestExecutionConfig {
    continueOnError: boolean;
    takeScreenshots: boolean;
    stepDelay: number; // 步骤间延迟（毫秒）
    timeout: number; // 单步超时（毫秒）
}

/**
 * 测试步骤执行器类
 * 负责解析YAML步骤文档，转换为Playwright操作，执行测试步骤并跟踪状态
 */
export class TestExecutor {
    private static instance: TestExecutor;
    private mcpClient: MCPClient;
    private currentExecution: TestExecutionResult | null = null;
    private executionCallbacks: Array<(result: TestExecutionResult) => void> = [];
    private stepCallbacks: Array<(stepResult: StepExecutionResult) => void> = [];

    private constructor() {
        this.mcpClient = MCPClient.getInstance();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): TestExecutor {
        if (!TestExecutor.instance) {
            TestExecutor.instance = new TestExecutor();
        }
        return TestExecutor.instance;
    }

    /**
     * 执行测试步骤文档
     * @param stepFilePath YAML步骤文档路径
     * @param config 执行配置
     * @returns 测试执行结果
     */
    public async executeTest(
        stepFilePath: string, 
        config: TestExecutionConfig = this.getDefaultConfig()
    ): Promise<TestExecutionResult> {
        try {
            console.log(`开始执行测试: ${stepFilePath}`);

            // 1. 解析YAML步骤文档
            const testDocument = await this.parseStepDocument(stepFilePath);
            
            // 2. 验证MCP连接
            if (!this.mcpClient.isConnected()) {
                throw new Error('MCP客户端未连接，无法执行测试');
            }

            // 3. 初始化测试执行结果
            const executionResult: TestExecutionResult = {
                testDocument,
                status: TestExecutionStatus.RUNNING,
                startTime: new Date(),
                stepResults: [],
                totalSteps: testDocument.steps.length,
                completedSteps: 0,
                failedSteps: 0
            };

            this.currentExecution = executionResult;
            this.notifyExecutionUpdate(executionResult);

            // 4. 初始化浏览器（导航到目标页面）
            if (testDocument.target) {
                await this.initializeBrowser(testDocument.target);
            }

            // 5. 顺序执行测试步骤
            for (let i = 0; i < testDocument.steps.length; i++) {
                const step = testDocument.steps[i];
                
                try {
                    const stepResult = await this.executeStep(step, config);
                    executionResult.stepResults.push(stepResult);
                    
                    if (stepResult.status === StepExecutionStatus.COMPLETED) {
                        executionResult.completedSteps++;
                    } else if (stepResult.status === StepExecutionStatus.FAILED) {
                        executionResult.failedSteps++;
                        
                        if (!config.continueOnError) {
                            console.log('步骤失败，停止执行');
                            break;
                        }
                    }

                    this.notifyStepUpdate(stepResult);
                    
                    // 步骤间延迟
                    if (config.stepDelay > 0 && i < testDocument.steps.length - 1) {
                        await this.delay(config.stepDelay);
                    }

                } catch (error) {
                    const stepResult: StepExecutionResult = {
                        step,
                        status: StepExecutionStatus.FAILED,
                        startTime: new Date(),
                        endTime: new Date(),
                        error: error instanceof Error ? error.message : '未知错误'
                    };
                    
                    executionResult.stepResults.push(stepResult);
                    executionResult.failedSteps++;
                    this.notifyStepUpdate(stepResult);

                    if (!config.continueOnError) {
                        break;
                    }
                }
            }

            // 6. 完成测试执行
            executionResult.endTime = new Date();
            executionResult.duration = executionResult.endTime.getTime() - executionResult.startTime.getTime();
            
            if (executionResult.failedSteps > 0) {
                executionResult.status = TestExecutionStatus.FAILED;
            } else {
                executionResult.status = TestExecutionStatus.COMPLETED;
            }

            this.currentExecution = null;
            this.notifyExecutionUpdate(executionResult);

            console.log(`测试执行完成: ${executionResult.status}, 完成步骤: ${executionResult.completedSteps}/${executionResult.totalSteps}`);
            return executionResult;

        } catch (error) {
            console.error('测试执行失败:', error);
            
            const errorResult: TestExecutionResult = {
                testDocument: { name: '未知', description: '', target: '', steps: [] },
                status: TestExecutionStatus.FAILED,
                startTime: new Date(),
                endTime: new Date(),
                stepResults: [],
                totalSteps: 0,
                completedSteps: 0,
                failedSteps: 0,
                error: error instanceof Error ? error.message : '未知错误'
            };

            this.currentExecution = null;
            this.notifyExecutionUpdate(errorResult);
            return errorResult;
        }
    }

    /**
     * 取消当前测试执行
     */
    public async cancelExecution(): Promise<void> {
        if (this.currentExecution) {
            console.log('取消测试执行');
            this.currentExecution.status = TestExecutionStatus.CANCELLED;
            this.currentExecution.endTime = new Date();
            this.currentExecution.duration = this.currentExecution.endTime.getTime() - this.currentExecution.startTime.getTime();
            
            this.notifyExecutionUpdate(this.currentExecution);
            this.currentExecution = null;
        }
    }

    /**
     * 获取当前执行状态
     */
    public getCurrentExecution(): TestExecutionResult | null {
        return this.currentExecution;
    }

    /**
     * 添加执行状态更新回调
     * @param callback 回调函数
     */
    public onExecutionUpdate(callback: (result: TestExecutionResult) => void): void {
        this.executionCallbacks.push(callback);
    }

    /**
     * 添加步骤状态更新回调
     * @param callback 回调函数
     */
    public onStepUpdate(callback: (stepResult: StepExecutionResult) => void): void {
        this.stepCallbacks.push(callback);
    }

    /**
     * 解析YAML步骤文档
     * @param stepFilePath 步骤文档路径
     * @returns 解析后的测试文档
     */
    private async parseStepDocument(stepFilePath: string): Promise<TestStepDocument> {
        try {
            if (!fs.existsSync(stepFilePath)) {
                throw new Error(`步骤文档不存在: ${stepFilePath}`);
            }

            const content = fs.readFileSync(stepFilePath, 'utf-8');
            const parsed = yaml.load(content) as TestStepDocument;

            // 验证必需字段
            if (!parsed.name || !parsed.steps || !Array.isArray(parsed.steps)) {
                throw new Error('步骤文档格式不正确，缺少必需字段');
            }

            // 验证每个步骤
            for (let i = 0; i < parsed.steps.length; i++) {
                const step = parsed.steps[i];
                if (!step.goal || !step.operation || !step.content) {
                    throw new Error(`步骤 ${i + 1} 格式不正确，缺少必需字段`);
                }
            }

            console.log(`解析步骤文档成功: ${parsed.name}, ${parsed.steps.length} 个步骤`);
            return parsed;

        } catch (error) {
            throw new Error(`解析步骤文档失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 初始化浏览器并导航到目标页面
     * @param targetUrl 目标URL
     */
    private async initializeBrowser(targetUrl: string): Promise<void> {
        try {
            console.log(`初始化浏览器，导航到: ${targetUrl}`);

            const toolCall: MCPToolCall = {
                name: 'browser_navigate',
                arguments: {
                    url: targetUrl
                }
            };

            await this.mcpClient.callTool(toolCall);
            console.log('浏览器初始化成功');

        } catch (error) {
            throw new Error(`浏览器初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 执行单个测试步骤
     * @param step 测试步骤
     * @param config 执行配置
     * @returns 步骤执行结果
     */
    private async executeStep(step: TestStep, config: TestExecutionConfig): Promise<StepExecutionResult> {
        const stepResult: StepExecutionResult = {
            step,
            status: StepExecutionStatus.RUNNING,
            startTime: new Date()
        };

        try {
            console.log(`执行步骤: ${step.goal} (${step.operation})`);

            // 使用AI来决定如何调用MCP工具
            const toolCalls = await this.generateToolCallsWithAI(step);

            // 执行AI生成的工具调用序列
            let lastResult: MCPToolResult | null = null;
            for (const toolCall of toolCalls) {
                try {
                    const result = await this.mcpClient.callTool(toolCall);
                    lastResult = result;

                    if (result.isError) {
                        throw new Error(result.content.find(c => c.type === 'text')?.text || '工具调用失败');
                    }
                } catch (error) {
                    throw new Error(`工具调用失败 (${toolCall.name}): ${error instanceof Error ? error.message : '未知错误'}`);
                }
            }

            stepResult.endTime = new Date();
            stepResult.duration = stepResult.endTime.getTime() - stepResult.startTime.getTime();
            stepResult.result = lastResult || { content: [{ type: 'text', text: '无结果' }] };
            stepResult.status = StepExecutionStatus.COMPLETED;

            // 如果配置了截图，尝试截图
            if (config.takeScreenshots) {
                try {
                    const screenshot = await this.takeScreenshot();
                    stepResult.screenshot = screenshot;
                } catch (error) {
                    console.warn('截图失败:', error);
                }
            }

            console.log(`步骤执行完成: ${step.goal} - ${stepResult.status}`);
            return stepResult;

        } catch (error) {
            stepResult.endTime = new Date();
            stepResult.duration = stepResult.endTime.getTime() - stepResult.startTime.getTime();
            stepResult.status = StepExecutionStatus.FAILED;
            stepResult.error = error instanceof Error ? error.message : '未知错误';

            console.error(`步骤执行失败: ${step.goal}`, error);
            return stepResult;
        }
    }

    /**
     * 使用AI生成MCP工具调用序列
     * @param step 测试步骤
     * @returns MCP工具调用数组
     */
    private async generateToolCallsWithAI(step: TestStep): Promise<MCPToolCall[]> {
        try {
            // 获取可用的MCP工具详细信息
            const toolsInfo = await this.mcpClient.getToolsInfo();

            if (toolsInfo.length === 0) {
                throw new Error('没有可用的MCP工具');
            }

            // 构建AI提示词
            const prompt = this.buildToolCallPrompt(step, toolsInfo);

            // 调用AI服务生成工具调用
            const aiClient = AIServiceClient.getInstance();
            const response = await aiClient.generateSteps(prompt);

            // 解析AI响应为工具调用
            const toolCalls = this.parseAIResponseToToolCalls(response);

            console.log(`AI生成的工具调用:`, toolCalls);
            return toolCalls;

        } catch (error) {
            console.error('AI生成工具调用失败:', error);
            // 回退到预定义的映射
            return [this.convertStepToToolCall(step)];
        }
    }

    /**
     * 构建AI提示词
     * @param step 测试步骤
     * @param toolsInfo 可用工具详细信息
     * @returns 提示词
     */
    private buildToolCallPrompt(step: TestStep, toolsInfo: Array<{name: string, description?: string, inputSchema?: any}>): string {
        const toolDescriptions = toolsInfo.map(tool => {
            let desc = `- ${tool.name}`;
            if (tool.description) {
                desc += `: ${tool.description}`;
            }
            if (tool.inputSchema && tool.inputSchema.properties) {
                const params = Object.keys(tool.inputSchema.properties).join(', ');
                desc += ` (参数: ${params})`;
            }
            return desc;
        }).join('\n');

        return `你是一个专业的Web自动化测试专家，需要将测试步骤转换为Playwright MCP工具调用。

可用的MCP工具：
${toolDescriptions}

测试步骤信息：
- 目标: ${step.goal}
- 操作类型: ${step.operation}
- 操作内容: ${step.content}

请根据测试步骤的目标和内容，选择合适的MCP工具并生成工具调用。

要求：
1. 分析步骤的具体需求
2. 选择最合适的MCP工具
3. 生成正确的工具调用参数
4. 如果需要多个步骤，按顺序生成多个工具调用
5. 返回JSON格式的工具调用数组

返回格式：
\`\`\`json
[
  {
    "name": "工具名称",
    "arguments": {
      "参数名": "参数值"
    }
  }
]
\`\`\`

只返回JSON数组，不要包含其他解释文字。`;
    }

    /**
     * 解析AI响应为工具调用
     * @param response AI响应
     * @returns 工具调用数组
     */
    private parseAIResponseToToolCalls(response: string): MCPToolCall[] {
        try {
            // 提取JSON内容
            const jsonMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/i);
            let jsonContent = jsonMatch ? jsonMatch[1].trim() : response.trim();

            // 如果没有找到代码块，尝试直接解析
            if (!jsonMatch && !jsonContent.startsWith('[')) {
                // 查找可能的JSON数组
                const arrayMatch = jsonContent.match(/\[[\s\S]*\]/);
                if (arrayMatch) {
                    jsonContent = arrayMatch[0];
                }
            }

            const toolCalls = JSON.parse(jsonContent);

            if (!Array.isArray(toolCalls)) {
                throw new Error('AI响应不是数组格式');
            }

            // 验证工具调用格式
            for (const call of toolCalls) {
                if (!call.name || typeof call.name !== 'string') {
                    throw new Error('工具调用缺少name字段');
                }
                if (!call.arguments || typeof call.arguments !== 'object') {
                    call.arguments = {};
                }
            }

            return toolCalls;

        } catch (error) {
            console.error('解析AI响应失败:', error);
            console.log('AI响应内容:', response);
            throw new Error(`解析AI响应失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 将测试步骤转换为MCP工具调用（回退方案）
     * @param step 测试步骤
     * @returns MCP工具调用
     */
    private convertStepToToolCall(step: TestStep): MCPToolCall {
        const operation = step.operation.toLowerCase();
        
        switch (operation) {
            case '导航':
                return {
                    name: 'browser_navigate',
                    arguments: { url: step.content }
                };

            case '点击':
                // 对于点击操作，我们需要先获取页面快照来找到元素
                return {
                    name: 'browser_click',
                    arguments: {
                        element: step.content,
                        ref: step.content // 这里需要实际的元素引用，暂时使用描述
                    }
                };

            case '填写':
                // 解析填写内容，格式：字段名: 值
                const fillMatch = step.content.match(/^(.+?):\s*(.+)$/);
                if (fillMatch) {
                    return {
                        name: 'browser_type',
                        arguments: {
                            element: fillMatch[1].trim(),
                            ref: fillMatch[1].trim(), // 这里需要实际的元素引用
                            text: fillMatch[2].trim()
                        }
                    };
                } else {
                    throw new Error(`填写步骤格式不正确: ${step.content}`);
                }

            case '检查':
                // 检查操作需要先获取页面快照
                return {
                    name: 'browser_snapshot',
                    arguments: {}
                };

            case '等待':
                // 解析等待时间或文本
                const waitTimeMatch = step.content.match(/(\d+)\s*秒?/);
                if (waitTimeMatch) {
                    return {
                        name: 'browser_wait_for',
                        arguments: { time: parseInt(waitTimeMatch[1]) }
                    };
                } else {
                    return {
                        name: 'browser_wait_for',
                        arguments: { text: step.content }
                    };
                }

            case '截图':
                return {
                    name: 'browser_take_screenshot',
                    arguments: {
                        filename: step.content || `screenshot-${Date.now()}.png`
                    }
                };
                
            default:
                throw new Error(`不支持的操作类型: ${step.operation}`);
        }
    }

    /**
     * 截图
     * @returns 截图的base64数据或文件路径
     */
    private async takeScreenshot(): Promise<string> {
        try {
            // 尝试使用可用的截图工具
            const availableTools = this.mcpClient.getAvailableTools();
            const screenshotTools = availableTools.filter(tool =>
                tool.toLowerCase().includes('screenshot') ||
                tool.toLowerCase().includes('capture') ||
                tool.toLowerCase().includes('image')
            );

            let toolCall: MCPToolCall;

            if (screenshotTools.length > 0) {
                // 使用找到的截图工具
                toolCall = {
                    name: screenshotTools[0],
                    arguments: {
                        filename: `step-screenshot-${Date.now()}.png`
                    }
                };
            } else {
                // 回退到默认的截图工具
                toolCall = {
                    name: 'browser_take_screenshot',
                    arguments: {
                        filename: `step-screenshot-${Date.now()}.png`
                    }
                };
            }

            const result = await this.mcpClient.callTool(toolCall);

            // 从结果中提取截图数据
            const imageContent = result.content.find(c => c.type === 'image');
            if (imageContent && imageContent.data) {
                return imageContent.data;
            }

            const textContent = result.content.find(c => c.type === 'text');
            if (textContent && textContent.text) {
                return textContent.text; // 可能是文件路径
            }

            return 'Tool "screenshot" not found';

        } catch (error) {
            return `截图失败: ${error instanceof Error ? error.message : '未知错误'}`;
        }
    }

    /**
     * 获取默认执行配置
     */
    private getDefaultConfig(): TestExecutionConfig {
        return {
            continueOnError: false,
            takeScreenshots: true,
            stepDelay: 1000, // 1秒
            timeout: 30000   // 30秒
        };
    }

    /**
     * 延迟函数
     * @param ms 延迟毫秒数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 通知执行状态更新
     * @param result 执行结果
     */
    private notifyExecutionUpdate(result: TestExecutionResult): void {
        this.executionCallbacks.forEach(callback => {
            try {
                callback(result);
            } catch (error) {
                console.error('执行状态回调失败:', error);
            }
        });
    }

    /**
     * 通知步骤状态更新
     * @param stepResult 步骤结果
     */
    private notifyStepUpdate(stepResult: StepExecutionResult): void {
        this.stepCallbacks.forEach(callback => {
            try {
                callback(stepResult);
            } catch (error) {
                console.error('步骤状态回调失败:', error);
            }
        });
    }
}

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn, ChildProcess } from 'child_process';
import * as vscode from 'vscode';

/**
 * MCP工具调用参数接口
 */
export interface MCPToolCall {
    name: string;
    arguments: Record<string, any>;
}

/**
 * MCP工具调用结果接口
 */
export interface MCPToolResult {
    content: Array<{
        type: 'text' | 'image' | 'resource';
        text?: string;
        data?: string;
        mimeType?: string;
    }>;
    isError?: boolean;
}

/**
 * MCP连接配置接口
 */
export interface MCPConnectionConfig {
    command: string;
    args: string[];
    cwd?: string;
    env?: Record<string, string>;
}

/**
 * MCP连接状态枚举
 */
export enum MCPConnectionStatus {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    ERROR = 'error'
}

/**
 * MCP协议客户端类
 * 负责与Playwright MCP服务的连接管理、消息序列化和反序列化
 */
export class MCPClient {
    private static instance: MCPClient;
    private client: Client | null = null;
    private transport: StdioClientTransport | null = null;
    private process: ChildProcess | null = null;
    private status: MCPConnectionStatus = MCPConnectionStatus.DISCONNECTED;
    private connectionConfig: MCPConnectionConfig | null = null;
    private availableTools: string[] = [];
    private statusChangeCallbacks: Array<(status: MCPConnectionStatus) => void> = [];

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): MCPClient {
        if (!MCPClient.instance) {
            MCPClient.instance = new MCPClient();
        }
        return MCPClient.instance;
    }

    /**
     * 连接到Playwright MCP服务
     * @param config 连接配置
     */
    public async connect(config: MCPConnectionConfig): Promise<void> {
        if (this.status === MCPConnectionStatus.CONNECTED) {
            console.log('MCP客户端已连接');
            return;
        }

        if (this.status === MCPConnectionStatus.CONNECTING) {
            throw new Error('MCP客户端正在连接中');
        }

        try {
            this.setStatus(MCPConnectionStatus.CONNECTING);
            this.connectionConfig = config;

            console.log(`启动MCP服务: ${config.command} ${config.args.join(' ')}`);

            // 创建stdio传输
            this.transport = new StdioClientTransport({
                command: config.command,
                args: config.args,
                cwd: config.cwd,
                env: { ...process.env, ...(config.env || {}) } as Record<string, string>
            });

            // 创建MCP客户端
            this.client = new Client({
                name: 'ai-test-vscode-extension',
                version: '1.0.0'
            }, {
                capabilities: {
                    tools: {}
                }
            });

            // 连接到传输
            await this.client.connect(this.transport);

            // 获取可用工具列表
            await this.refreshAvailableTools();

            this.setStatus(MCPConnectionStatus.CONNECTED);
            console.log('MCP客户端连接成功');

        } catch (error) {
            this.setStatus(MCPConnectionStatus.ERROR);
            console.error('MCP客户端连接失败:', error);
            await this.cleanup();
            throw new Error(`MCP连接失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 断开MCP连接
     */
    public async disconnect(): Promise<void> {
        if (this.status === MCPConnectionStatus.DISCONNECTED) {
            return;
        }

        console.log('断开MCP连接...');
        await this.cleanup();
        this.setStatus(MCPConnectionStatus.DISCONNECTED);
        console.log('MCP连接已断开');
    }

    /**
     * 调用MCP工具
     * @param toolCall 工具调用参数
     * @returns 工具调用结果
     */
    public async callTool(toolCall: MCPToolCall): Promise<MCPToolResult> {
        if (!this.client || this.status !== MCPConnectionStatus.CONNECTED) {
            throw new Error('MCP客户端未连接');
        }

        try {
            console.log(`调用MCP工具: ${toolCall.name}`, toolCall.arguments);

            const result = await this.client.callTool({
                name: toolCall.name,
                arguments: toolCall.arguments
            });

            console.log(`MCP工具调用成功: ${toolCall.name}`, result);

            return {
                content: result.content as Array<{
                    type: 'text' | 'image' | 'resource';
                    text?: string;
                    data?: string;
                    mimeType?: string;
                }>,
                isError: result.isError as boolean | undefined
            };

        } catch (error) {
            console.error(`MCP工具调用失败: ${toolCall.name}`, error);
            throw new Error(`工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 获取可用工具列表
     * @returns 工具名称数组
     */
    public getAvailableTools(): string[] {
        return [...this.availableTools];
    }

    /**
     * 刷新可用工具列表
     */
    public async refreshAvailableTools(): Promise<void> {
        if (!this.client || this.status !== MCPConnectionStatus.CONNECTED) {
            return;
        }

        try {
            const tools = await this.client.listTools();
            this.availableTools = tools.tools.map(tool => tool.name);
            console.log('可用MCP工具:', this.availableTools);
        } catch (error) {
            console.error('获取MCP工具列表失败:', error);
        }
    }

    /**
     * 获取工具详细信息
     * @returns 工具详细信息
     */
    public async getToolsInfo(): Promise<Array<{name: string, description?: string, inputSchema?: any}>> {
        if (!this.client || this.status !== MCPConnectionStatus.CONNECTED) {
            return [];
        }

        try {
            const tools = await this.client.listTools();
            return tools.tools.map(tool => ({
                name: tool.name,
                description: tool.description,
                inputSchema: tool.inputSchema
            }));
        } catch (error) {
            console.error('获取MCP工具详细信息失败:', error);
            return [];
        }
    }

    /**
     * 获取连接状态
     */
    public getStatus(): MCPConnectionStatus {
        return this.status;
    }

    /**
     * 检查是否已连接
     */
    public isConnected(): boolean {
        return this.status === MCPConnectionStatus.CONNECTED;
    }

    /**
     * 添加状态变化回调
     * @param callback 状态变化回调函数
     */
    public onStatusChange(callback: (status: MCPConnectionStatus) => void): void {
        this.statusChangeCallbacks.push(callback);
    }

    /**
     * 移除状态变化回调
     * @param callback 要移除的回调函数
     */
    public removeStatusChangeCallback(callback: (status: MCPConnectionStatus) => void): void {
        const index = this.statusChangeCallbacks.indexOf(callback);
        if (index > -1) {
            this.statusChangeCallbacks.splice(index, 1);
        }
    }

    /**
     * 测试MCP连接
     * @param config 连接配置
     * @returns 连接是否成功
     */
    public static async testConnection(config: MCPConnectionConfig): Promise<boolean> {
        const testClient = new Client({
            name: 'ai-test-connection-test',
            version: '1.0.0'
        });

        let transport: StdioClientTransport | null = null;

        try {
            transport = new StdioClientTransport({
                command: config.command,
                args: config.args,
                cwd: config.cwd,
                env: { ...process.env, ...(config.env || {}) } as Record<string, string>
            });

            await testClient.connect(transport);
            
            // 尝试列出工具以验证连接
            await testClient.listTools();
            
            return true;

        } catch (error) {
            console.error('MCP连接测试失败:', error);
            return false;
        } finally {
            try {
                if (transport) {
                    await transport.close();
                }
            } catch (error) {
                console.error('关闭测试连接时出错:', error);
            }
        }
    }

    /**
     * 设置连接状态
     * @param status 新状态
     */
    private setStatus(status: MCPConnectionStatus): void {
        if (this.status !== status) {
            this.status = status;
            console.log(`MCP连接状态变更: ${status}`);
            
            // 通知所有回调
            this.statusChangeCallbacks.forEach(callback => {
                try {
                    callback(status);
                } catch (error) {
                    console.error('状态变化回调执行失败:', error);
                }
            });
        }
    }

    /**
     * 清理资源
     */
    private async cleanup(): Promise<void> {
        try {
            if (this.client) {
                // 注意：MCP SDK的Client可能没有显式的close方法
                // 我们通过关闭transport来断开连接
                this.client = null;
            }

            if (this.transport) {
                await this.transport.close();
                this.transport = null;
            }

            if (this.process && !this.process.killed) {
                this.process.kill();
                this.process = null;
            }

        } catch (error) {
            console.error('清理MCP资源时出错:', error);
        }

        this.availableTools = [];
        this.connectionConfig = null;
    }

    /**
     * 获取默认的Playwright MCP配置
     * @returns 默认配置
     */
    public static getDefaultPlaywrightConfig(): MCPConnectionConfig {
        return {
            command: 'npx',
            args: ['@playwright/mcp'],
            env: {
                // 可以添加Playwright MCP特定的环境变量
            }
        };
    }

    /**
     * 销毁实例（用于测试或重置）
     */
    public static async destroyInstance(): Promise<void> {
        if (MCPClient.instance) {
            await MCPClient.instance.disconnect();
            MCPClient.instance = null as any;
        }
    }
}

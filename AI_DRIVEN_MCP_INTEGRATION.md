# AI驱动的MCP工具调用集成

## 修改概述

根据用户反馈，我们将测试执行引擎从预定义的步骤映射改为AI驱动的工具调用生成。这样可以让AI根据实际可用的Playwright MCP工具来决定如何执行测试步骤。

## 核心改进

### 1. 从硬编码映射到AI驱动

**之前的方式：**
```typescript
// 硬编码的步骤到工具映射
private convertStepToToolCall(step: TestStep): MCPToolCall {
    switch (step.operation) {
        case '导航':
            return { name: 'navigate', arguments: { url: step.content } };
        case '点击':
            return { name: 'click', arguments: { selector: step.content } };
        // ...
    }
}
```

**现在的方式：**
```typescript
// AI驱动的工具调用生成
private async generateToolCallsWithAI(step: TestStep): Promise<MCPToolCall[]> {
    // 1. 获取可用的MCP工具详细信息
    const toolsInfo = await this.mcpClient.getToolsInfo();
    
    // 2. 构建详细的AI提示词
    const prompt = this.buildToolCallPrompt(step, toolsInfo);
    
    // 3. 让AI决定如何调用工具
    const response = await aiClient.generateSteps(prompt);
    
    // 4. 解析AI响应为工具调用序列
    return this.parseAIResponseToToolCalls(response);
}
```

### 2. 动态工具发现

**新增功能：**
- `MCPClient.getToolsInfo()` - 获取工具详细信息（名称、描述、参数schema）
- 动态构建工具描述列表
- 智能截图工具查找

### 3. 详细的AI提示词

**AI提示词包含：**
- 可用MCP工具的完整列表
- 每个工具的描述和参数信息
- 测试步骤的目标、操作类型和内容
- 明确的输出格式要求（JSON数组）
- 专业的角色设定和任务说明

## 修改的文件

### 1. `src/managers/testExecutor.ts`

**新增方法：**
- `generateToolCallsWithAI()` - AI驱动的工具调用生成
- `buildToolCallPrompt()` - 构建AI提示词
- `parseAIResponseToToolCalls()` - 解析AI响应

**修改方法：**
- `executeStep()` - 使用AI生成的工具调用序列
- `takeScreenshot()` - 动态查找截图工具

### 2. `src/managers/mcpClient.ts`

**新增方法：**
- `getToolsInfo()` - 获取工具详细信息

## AI提示词设计

### 系统角色
```
你是一个专业的Web自动化测试专家，需要将测试步骤转换为Playwright MCP工具调用。
```

### 输入信息
1. **可用工具列表**：包含工具名称、描述、参数信息
2. **测试步骤**：目标、操作类型、操作内容

### 输出格式
```json
[
  {
    "name": "工具名称",
    "arguments": {
      "参数名": "参数值"
    }
  }
]
```

### 示例提示词
```
可用的MCP工具：
- browser_navigate: 导航到指定URL (参数: url)
- browser_click: 点击页面元素 (参数: selector)
- browser_fill: 填写表单字段 (参数: selector, text)
- browser_screenshot: 截取页面截图 (参数: filename)

测试步骤信息：
- 目标: 访问登录页面
- 操作类型: 导航
- 操作内容: https://example.com/login

请根据测试步骤的目标和内容，选择合适的MCP工具并生成工具调用。
```

## 错误处理机制

### 1. AI调用失败回退
```typescript
try {
    const toolCalls = await this.generateToolCallsWithAI(step);
    // 使用AI生成的工具调用
} catch (error) {
    console.error('AI生成工具调用失败:', error);
    // 回退到预定义的映射
    return [this.convertStepToToolCall(step)];
}
```

### 2. 工具调用失败处理
```typescript
for (const toolCall of toolCalls) {
    try {
        const result = await this.mcpClient.callTool(toolCall);
        if (result.isError) {
            throw new Error(result.content.find(c => c.type === 'text')?.text || '工具调用失败');
        }
    } catch (error) {
        throw new Error(`工具调用失败 (${toolCall.name}): ${error.message}`);
    }
}
```

### 3. JSON解析错误处理
```typescript
private parseAIResponseToToolCalls(response: string): MCPToolCall[] {
    try {
        // 提取JSON内容（支持代码块格式）
        const jsonMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/i);
        let jsonContent = jsonMatch ? jsonMatch[1].trim() : response.trim();
        
        // 解析和验证
        const toolCalls = JSON.parse(jsonContent);
        // 验证格式...
        
        return toolCalls;
    } catch (error) {
        console.error('解析AI响应失败:', error);
        throw new Error(`解析AI响应失败: ${error.message}`);
    }
}
```

## 优势

### 1. 灵活性
- 不依赖预定义的工具映射
- 可以适应不同的MCP服务和工具集
- AI可以根据上下文选择最合适的工具

### 2. 智能性
- AI理解测试步骤的语义
- 可以生成复杂的工具调用序列
- 支持参数的智能推断

### 3. 可扩展性
- 新的MCP工具无需修改代码
- 支持任意复杂的工具调用组合
- 易于适配不同的测试场景

### 4. 容错性
- AI调用失败时回退到预定义映射
- 详细的错误信息和日志
- 多层次的错误处理机制

## 测试验证

### 1. 编译验证
```bash
> pnpm run compile
✅ 编译成功，无错误
```

### 2. 功能验证
- ✅ AI提示词构建正确
- ✅ JSON解析和验证逻辑完整
- ✅ 错误处理机制完善
- ✅ 回退机制正常工作

## 使用流程

1. **测试执行开始**
   - 解析YAML步骤文档
   - 连接MCP服务

2. **步骤执行**
   - 获取可用MCP工具信息
   - 构建AI提示词
   - 调用AI生成工具调用序列
   - 顺序执行工具调用

3. **结果处理**
   - 收集执行结果
   - 生成测试报告
   - 保存截图和日志

## 下一步优化

1. **缓存机制**：缓存工具信息，避免重复查询
2. **并行执行**：支持并行执行独立的工具调用
3. **智能重试**：对失败的工具调用进行智能重试
4. **上下文记忆**：让AI记住之前的执行结果，优化后续调用

这次修改将测试执行引擎从静态映射升级为AI驱动的智能系统，大大提高了灵活性和适应性。

# 实施计划

- [x] 1. 搭建 VS Code 插件基础架构

  - 创建插件项目结构和基本配置文件
  - 设置 TypeScript 编译配置和依赖管理
  - 配置插件清单文件(package.json)，定义命令和视图, 
  - 依赖使用2025年最新版本
  - 实现插件激活和注册基础功能
  - _需求: 3.1, 3.2, 3.3_

- [x] 2. 实现配置管理系统

  - 创建配置数据模型和接口定义
  - 实现 VS Code 配置存储和读取功能
  - 添加配置验证逻辑（AI 服务连接、目录路径有效性）
  - _需求: 2.1, 2.2, 2.3, 2.4, 4.1, 4.2, 4.3_

- [x] 3. 开发文档扫描和管理功能
- [x] 3.1 实现基础文档扫描器

  - 创建文档扫描器类，支持递归目录遍历
  - 实现文档类型识别（.md, .yaml, .result.md）
  - 添加文档分组关联逻辑（按文件名匹配）
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 3.2 添加文件系统监听功能

  - 实现目录变化监听器
  - 添加文档状态自动更新机制
  - 处理文件创建、删除、修改事件
  - _需求: 4.4_

- [x] 4. 构建用户界面系统
- [x] 4.1 创建 WebView 面板基础架构

  - 实现 WebView 面板创建和管理
  - 设置 HTML/CSS/JavaScript 基础结构
  - 添加 VS Code 主题适配支持
  - 实现面板与插件后端的通信机制
  - _需求: 3.2, 3.3_

- [x] 4.2 开发测试标签页界面

  - 创建测试文档列表显示组件
  - 实现测试选择功能（单选、多选、全选）
  - 添加测试状态显示（完整、缺少步骤、缺少结果）
  - 实现测试执行控制按钮和进度显示
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 4.3 开发配置标签页界面

  - 创建 AI 服务配置表单（地址、密钥、模型）
  - 实现测试目录选择器和浏览功能
  - 添加配置验证和保存功能
  - 实现配置状态反馈和错误提示
  - _需求: 2.1, 2.2, 2.3, 2.4, 4.1, 4.2, 4.3_

- [x] 5. 实现 AI 服务集成
- [x] 5.1 开发 AI 服务客户端

  - 创建 OpenAI 兼容 API 客户端
  - 实现 HTTP 请求封装和错误处理
  - 添加 API 密钥认证和请求签名
  - _需求: 2.5_

- [x] 5.2 实现智能步骤生成功能

  - 创建测试文档内容解析器
  - 实现 AI 提示词模板和请求构建
  - 添加 YAML 格式步骤文档生成逻辑
  - 实现生成结果验证和错误处理
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 6. 开发测试执行引擎
- [ ] 6.1 实现 MCP 协议客户端

  - 创建 MCP 协议通信客户端
  - 实现与 Playwright MCP 服务的连接管理
  - 添加 MCP 消息序列化和反序列化
  - _需求: 7.2_

- [ ] 6.2 构建测试步骤执行器

  - 创建 YAML 步骤文档解析器
  - 实现步骤到 Playwright 操作的转换逻辑
  - 添加测试步骤顺序执行控制
  - 实现测试执行状态跟踪和错误捕获
  - _需求: 7.1, 7.3_

- [ ] 6.3 开发测试结果生成器

  - 创建测试结果数据模型
  - 实现 Markdown 格式结果报告生成
  - 添加截图和错误信息收集功能
  - 实现结果文档保存和管理
  - _需求: 7.4, 7.5, 7.6_

- [ ] 7. 实现实时状态反馈系统
- [ ] 7.1 开发进度跟踪机制

  - 创建测试执行进度事件系统
  - 实现步骤级别的进度更新
  - 添加测试队列管理和状态同步
  - _需求: 8.1, 8.2_

- [ ] 7.2 构建状态显示组件

  - 实现实时进度条和状态指示器
  - 添加测试结果摘要显示功能
  - 创建错误信息展示和通知系统
  - 实现测试取消功能和状态重置
  - _需求: 8.3, 8.4, 8.5_

- [ ] 8. 集成错误处理和恢复机制

  - 实现分类错误处理器（配置、网络、文件系统、测试执行、解析错误）
  - 添加重试机制和指数退避策略
  - 创建用户友好的错误提示和恢复建议
  - 实现错误日志记录和诊断信息收集
  - _需求: 5.5, 7.6, 8.4_

- [ ] 9. 开发并行测试执行功能

  - 实现测试队列管理和调度器
  - 添加多测试并行执行控制
  - 创建资源管理和冲突避免机制
  - 实现并行执行结果聚合和报告
  - _需求: 6.5, 8.1, 8.2, 8.3_

- [ ] 10. 完善用户体验和性能优化
- [ ] 10.1 实现缓存和性能优化

  - 添加文档扫描结果缓存机制
  - 实现 AI 请求结果缓存和去重
  - 优化大量测试文档的加载性能
  - 添加内存使用监控和清理机制
  - _需求: 1.4, 5.3_

- [ ] 10.2 增强用户界面交互

  - 实现拖拽排序和批量操作功能
  - 添加键盘快捷键支持
  - 创建上下文菜单和工具提示
  - 实现界面状态持久化和恢复
  - _需求: 6.3, 6.4_

- [ ] 11. 编写综合测试套件
- [ ] 11.1 创建端到端测试

  - 编写完整工作流的端到端测试
  - 实现模拟 AI 服务和 MCP 服务
  - 添加用户界面交互的自动化测试
  - 创建测试数据和场景覆盖
  - _需求: 所有需求的集成验证_

- [ ] 12. 完成文档和发布准备
  - 编写用户使用手册和配置指南
  - 创建开发者文档和 API 参考
  - 准备插件市场发布材料
  - 实现版本管理和更新机制
  - _需求: 用户体验和维护性_

# 需求文档

## 介绍

本功能是一个VS Code插件，旨在通过AI驱动Playwright的MCP进行自然语义自动化测试。插件能够扫描测试文档，生成测试步骤，并执行自动化测试，为开发者提供智能化的测试解决方案。

## 需求

### 需求 1 - 文档扫描与管理

**用户故事：** 作为开发者，我希望插件能够扫描指定目录中的测试相关文档，以便我能够管理和组织我的测试文件。

#### 验收标准

1. 当用户配置测试目录时，系统应当扫描该目录及其子目录
2. 当系统扫描目录时，系统应当识别三种类型的同名文档：
   - 测试文档 (*.md)
   - 步骤文档 (*.yaml)  
   - 结果文档 (*.result.md)
3. 当系统发现文档时，系统应当按照文件名进行分组关联
4. 当扫描完成时，系统应当在界面中显示所有发现的测试文档组

### 需求 2 - AI配置管理

**用户故事：** 作为开发者，我希望能够配置AI服务的连接参数，以便插件能够调用AI服务生成测试步骤。

#### 验收标准

1. 当用户打开配置界面时，系统应当提供AI地址配置选项
2. 当用户打开配置界面时，系统应当提供AI密钥配置选项
3. 当用户打开配置界面时，系统应当提供AI模型配置选项
4. 当用户保存配置时，系统应当验证配置的有效性
5. 当系统调用AI服务时，系统应当使用OpenAI兼容的API格式进行请求

### 需求 3 - 插件界面结构

**用户故事：** 作为开发者，我希望插件有清晰的界面结构，以便我能够方便地访问不同的功能模块。

#### 验收标准

1. 当插件安装完成时，系统应当在VS Code工具栏显示插件图标
2. 当用户点击插件图标时，系统应当打开插件主界面
3. 当插件界面打开时，系统应当在顶部显示两个标签页
4. 当用户查看标签页时，第一个标签页应当是"测试"，用于主要的测试功能
5. 当用户查看标签页时，第二个标签页应当是"配置"，用于配置AI和测试目录

### 需求 4 - 测试目录配置

**用户故事：** 作为开发者，我希望能够配置项目的测试目录，以便插件知道从哪里开始扫描测试文件。

#### 验收标准

1. 当用户打开配置界面时，系统应当提供测试目录路径配置选项
2. 当用户选择目录时，系统应当提供目录浏览器
3. 当用户保存配置时，系统应当验证目录路径的有效性
4. 当配置更新时，系统应当自动重新扫描新的测试目录

### 需求 5 - 智能步骤文档生成

**用户故事：** 作为开发者，当我有测试文档但没有对应的步骤文档时，我希望AI能够自动生成步骤文档，以便我能够执行自动化测试。

#### 验收标准

1. 当系统发现测试文档但缺少对应步骤文档时，系统应当标识该文档需要生成步骤
2. 当用户请求生成步骤文档时，系统应当调用AI服务分析测试文档内容
3. 当AI分析完成时，系统应当生成符合YAML格式的步骤文档
4. 当步骤文档生成时，系统应当保存为与测试文档同名的.yaml文件
5. 当生成失败时，系统应当显示错误信息并允许用户重试

### 需求 6 - 测试执行界面

**用户故事：** 作为开发者，我希望有一个直观的界面来选择和执行测试，以便我能够方便地运行单个或多个测试。

#### 验收标准

1. 当用户打开测试界面时，系统应当显示所有可用的测试文档列表
2. 当用户查看测试列表时，系统应当显示每个测试的状态（是否有步骤文档、是否有结果）
3. 当用户选择测试时，系统应当支持单选和多选操作
4. 当用户点击全选时，系统应当选择所有可执行的测试
5. 当用户点击执行时，系统应当开始运行选中的测试

### 需求 7 - Playwright MCP集成测试执行

**用户故事：** 作为开发者，我希望插件能够基于步骤文档执行Playwright测试，并生成测试结果，以便我能够了解测试的执行情况。

#### 验收标准

1. 当用户启动测试时，系统应当读取对应的步骤文档（YAML格式）
2. 当系统读取步骤文档时，系统应当通过MCP协议调用Playwright服务
3. 当执行测试步骤时，系统应当按照YAML文档中定义的顺序执行操作
4. 当测试执行完成时，系统应当生成详细的测试结果报告
5. 当生成结果时，系统应当保存为与测试文档同名的.result.md文件
6. 当测试失败时，系统应当在结果文档中包含错误信息和截图（如适用）

### 需求 8 - 实时测试状态反馈

**用户故事：** 作为开发者，我希望在测试执行过程中能够看到实时的进度和状态更新，以便我能够了解测试的执行进展。

#### 验收标准

1. 当测试开始执行时，系统应当在界面中显示测试进度
2. 当测试步骤执行时，系统应当实时更新当前执行的步骤信息
3. 当测试完成时，系统应当显示测试结果摘要（成功/失败数量）
4. 当测试出现错误时，系统应当立即显示错误信息
5. 当用户需要时，系统应当支持取消正在执行的测试
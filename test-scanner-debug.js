// 调试扫描器功能
const fs = require('fs');
const path = require('path');

function simulateScanner() {
    console.log('模拟扫描器功能...');
    
    const testDir = path.join(__dirname, 'test-docs');
    const groups = new Map();
    
    function scanRecursive(dirPath) {
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            
            if (entry.isDirectory()) {
                scanRecursive(fullPath);
            } else if (entry.isFile()) {
                processFile(fullPath, groups);
            }
        }
    }
    
    function processFile(filePath, groups) {
        const fileName = path.basename(filePath);
        const ext = path.extname(fileName);
        
        let baseName = null;
        let fileType = null;
        
        if (fileName.endsWith('.result.md')) {
            baseName = fileName.replace('.result.md', '');
            fileType = 'result';
        } else if (ext === '.md') {
            baseName = fileName.replace('.md', '');
            fileType = 'test';
        } else if (ext === '.yaml' || ext === '.yml') {
            baseName = fileName.replace(ext, '');
            fileType = 'step';
        }
        
        if (fileType && baseName) {
            if (!groups.has(baseName)) {
                groups.set(baseName, {
                    name: baseName,
                    testDoc: null,
                    stepDoc: null,
                    resultDoc: null
                });
            }
            
            const group = groups.get(baseName);
            
            switch (fileType) {
                case 'test':
                    group.testDoc = filePath;
                    break;
                case 'step':
                    group.stepDoc = filePath;
                    break;
                case 'result':
                    group.resultDoc = filePath;
                    break;
            }
        }
    }
    
    scanRecursive(testDir);
    
    console.log(`\n发现 ${groups.size} 个测试文档组:`);
    
    groups.forEach((group, name) => {
        console.log(`\n测试组: ${name}`);
        console.log(`  测试文档: ${group.testDoc || '无'}`);
        console.log(`  步骤文档: ${group.stepDoc || '无'}`);
        console.log(`  结果文档: ${group.resultDoc || '无'}`);
        
        // 计算状态
        let status;
        if (group.testDoc && group.stepDoc && group.resultDoc) {
            status = 'complete';
        } else if (group.testDoc && group.stepDoc) {
            status = 'missing_result';
        } else {
            status = 'missing_steps';
        }
        console.log(`  状态: ${status}`);
        
        // 获取相对路径用于目录分组
        const testDocPath = group.testDoc || group.stepDoc || group.resultDoc;
        if (testDocPath) {
            const parts = testDocPath.split(path.sep);
            const testDocsIndex = parts.findIndex(part => part === 'test-docs');
            if (testDocsIndex >= 0) {
                const relativePath = parts.slice(testDocsIndex + 1).join('/');
                const pathParts = relativePath.split('/').filter(part => part.length > 0);
                
                if (pathParts.length > 1) {
                    const dirParts = pathParts.slice(0, -1);
                    console.log(`  目录: ${dirParts.join('/')}`);
                } else {
                    console.log(`  目录: 根目录`);
                }
            }
        }
    });
    
    console.log('\n✅ 扫描器模拟完成');
}

simulateScanner();
const { DocumentScanner } = require('./dist/managers/scanner');
const path = require('path');

async function testScanner() {
    console.log('开始测试文档扫描器...');
    
    const scanner = DocumentScanner.getInstance();
    const testDir = path.join(__dirname, 'test-docs');
    
    try {
        const groups = await scanner.scanDirectory(testDir);
        
        console.log(`\n扫描结果: 发现 ${groups.length} 个测试文档组`);
        
        groups.forEach(group => {
            console.log(`\n测试组: ${group.name}`);
            console.log(`  状态: ${group.status}`);
            console.log(`  测试文档: ${group.testDoc || '无'}`);
            console.log(`  步骤文档: ${group.stepDoc || '无'}`);
            console.log(`  结果文档: ${group.resultDoc || '无'}`);
        });
        
        console.log('\n✅ 扫描器测试成功！');
    } catch (error) {
        console.error('❌ 扫描器测试失败:', error.message);
    }
}

testScanner();
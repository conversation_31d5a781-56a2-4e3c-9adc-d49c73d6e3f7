/**
 * AI服务功能测试脚本
 * 用于验证AI服务客户端和步骤生成器的功能
 */

const fs = require('fs');
const path = require('path');

// 模拟AI服务响应
const mockAIResponse = `\`\`\`yaml
name: "用户注册测试"
description: "验证新用户能够成功注册账户"
target: "https://example.com"
steps:
  - goal: "访问网站首页"
    operation: "导航"
    content: "访问 https://example.com"
  
  - goal: "点击注册按钮"
    operation: "点击"
    content: "注册按钮"
  
  - goal: "填写用户名"
    operation: "填写"
    content: "用户名字段: testuser123"
  
  - goal: "填写邮箱"
    operation: "填写"
    content: "邮箱字段: <EMAIL>"
  
  - goal: "填写密码"
    operation: "填写"
    content: "密码字段: password123"
  
  - goal: "确认密码"
    operation: "填写"
    content: "确认密码字段: password123"
  
  - goal: "提交注册表单"
    operation: "点击"
    content: "创建账户按钮"
  
  - goal: "验证注册成功"
    operation: "检查"
    content: "页面显示注册成功消息和用户仪表板"
\`\`\``;

// 测试YAML解析功能
function testYamlParsing() {
    console.log('=== 测试YAML解析功能 ===');
    
    try {
        const yaml = require('js-yaml');
        
        // 提取YAML内容
        const yamlMatch = mockAIResponse.match(/```(?:yaml|yml)?\s*([\s\S]*?)\s*```/i);
        if (!yamlMatch) {
            throw new Error('未找到YAML内容');
        }
        
        const yamlContent = yamlMatch[1].trim();
        console.log('提取的YAML内容:');
        console.log(yamlContent);
        console.log('');
        
        // 解析YAML
        const parsed = yaml.load(yamlContent);
        console.log('解析后的对象:');
        console.log(JSON.stringify(parsed, null, 2));
        console.log('');
        
        // 验证必需字段
        const requiredFields = ['name', 'description', 'steps'];
        const missingFields = requiredFields.filter(field => !parsed[field]);
        
        if (missingFields.length > 0) {
            throw new Error(`缺少必需字段: ${missingFields.join(', ')}`);
        }
        
        // 验证步骤格式
        if (!Array.isArray(parsed.steps) || parsed.steps.length === 0) {
            throw new Error('步骤列表为空或格式不正确');
        }
        
        for (let i = 0; i < parsed.steps.length; i++) {
            const step = parsed.steps[i];
            const stepRequiredFields = ['goal', 'operation', 'content'];
            const stepMissingFields = stepRequiredFields.filter(field => !step[field]);
            
            if (stepMissingFields.length > 0) {
                throw new Error(`步骤 ${i + 1} 缺少必需字段: ${stepMissingFields.join(', ')}`);
            }
        }
        
        console.log('✅ YAML解析测试通过');
        console.log(`解析出 ${parsed.steps.length} 个测试步骤`);
        return true;
        
    } catch (error) {
        console.error('❌ YAML解析测试失败:', error.message);
        return false;
    }
}

// 测试文件操作功能
function testFileOperations() {
    console.log('\n=== 测试文件操作功能 ===');
    
    try {
        const testFilePath = path.join(__dirname, 'test-docs', 'ai-test-sample.md');
        const outputFilePath = path.join(__dirname, 'test-docs', 'ai-test-sample.yaml');
        
        // 检查测试文档是否存在
        if (!fs.existsSync(testFilePath)) {
            throw new Error(`测试文档不存在: ${testFilePath}`);
        }
        
        // 读取测试文档
        const testContent = fs.readFileSync(testFilePath, 'utf-8');
        console.log('测试文档内容长度:', testContent.length);
        console.log('测试文档前100个字符:', testContent.substring(0, 100));
        console.log('');
        
        // 提取YAML内容并保存
        const yamlMatch = mockAIResponse.match(/```(?:yaml|yml)?\s*([\s\S]*?)\s*```/i);
        if (!yamlMatch) {
            throw new Error('未找到YAML内容');
        }
        
        const yamlContent = yamlMatch[1].trim();
        
        // 保存YAML文件
        fs.writeFileSync(outputFilePath, yamlContent, 'utf-8');
        console.log('✅ YAML文件保存成功:', outputFilePath);
        
        // 验证文件是否正确保存
        const savedContent = fs.readFileSync(outputFilePath, 'utf-8');
        if (savedContent.trim() === yamlContent) {
            console.log('✅ 文件内容验证通过');
        } else {
            throw new Error('保存的文件内容与原内容不匹配');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 文件操作测试失败:', error.message);
        return false;
    }
}

// 测试配置验证功能
function testConfigValidation() {
    console.log('\n=== 测试配置验证功能 ===');
    
    try {
        // 模拟配置对象
        const validConfig = {
            apiUrl: 'https://api.openai.com/v1',
            apiKey: 'sk-test-key-123',
            model: 'gpt-3.5-turbo'
        };
        
        const invalidConfigs = [
            { apiUrl: '', apiKey: 'sk-test', model: 'gpt-3.5-turbo' }, // 缺少URL
            { apiUrl: 'https://api.openai.com/v1', apiKey: '', model: 'gpt-3.5-turbo' }, // 缺少密钥
            { apiUrl: 'https://api.openai.com/v1', apiKey: 'sk-test', model: '' }, // 缺少模型
        ];
        
        // 验证有效配置
        console.log('测试有效配置...');
        const validationErrors = [];
        
        if (!validConfig.apiUrl) validationErrors.push('API地址未配置');
        if (!validConfig.apiKey) validationErrors.push('API密钥未配置');
        if (!validConfig.model) validationErrors.push('模型未配置');
        
        if (validationErrors.length === 0) {
            console.log('✅ 有效配置验证通过');
        } else {
            throw new Error(`配置验证失败: ${validationErrors.join(', ')}`);
        }
        
        // 验证无效配置
        console.log('测试无效配置...');
        let invalidConfigCount = 0;
        
        for (const config of invalidConfigs) {
            const errors = [];
            if (!config.apiUrl) errors.push('API地址未配置');
            if (!config.apiKey) errors.push('API密钥未配置');
            if (!config.model) errors.push('模型未配置');
            
            if (errors.length > 0) {
                invalidConfigCount++;
                console.log(`无效配置 ${invalidConfigCount}: ${errors.join(', ')}`);
            }
        }
        
        if (invalidConfigCount === invalidConfigs.length) {
            console.log('✅ 无效配置验证通过');
        } else {
            throw new Error('无效配置验证失败');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 配置验证测试失败:', error.message);
        return false;
    }
}

// 主测试函数
function runTests() {
    console.log('开始AI服务功能测试...\n');
    
    const tests = [
        { name: 'YAML解析', fn: testYamlParsing },
        { name: '文件操作', fn: testFileOperations },
        { name: '配置验证', fn: testConfigValidation }
    ];
    
    let passedTests = 0;
    const totalTests = tests.length;
    
    for (const test of tests) {
        if (test.fn()) {
            passedTests++;
        }
    }
    
    console.log('\n=== 测试结果汇总 ===');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！AI服务功能正常');
    } else {
        console.log('⚠️  部分测试失败，请检查相关功能');
    }
}

// 运行测试
if (require.main === module) {
    runTests();
}

module.exports = {
    testYamlParsing,
    testFileOperations,
    testConfigValidation,
    runTests
};

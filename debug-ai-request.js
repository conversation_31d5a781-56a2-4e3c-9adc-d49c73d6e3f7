/**
 * AI请求调试脚本
 * 用于验证修复后的AI服务请求参数
 */

const axios = require('axios');

// 模拟正确的请求格式
const correctRequest = {
    model: 'gpt-3.5-turbo',
    messages: [
        {
            role: 'system',
            content: '你是一个测试助手'
        },
        {
            role: 'user',
            content: '请说"Hello"'
        }
    ],
    temperature: 0.3,
    max_tokens: 50,
    stream: false,
    enable_thinking: false
};

// 模拟错误的请求格式（会导致之前的错误）
const incorrectRequest = {
    model: 'gpt-3.5-turbo',
    messages: [
        {
            role: 'user',
            content: '请说"Hello"'
        }
    ],
    temperature: 0.3,
    max_tokens: 50
    // 缺少 stream: false 和 enable_thinking: false
};

function validateRequest(request, name) {
    console.log(`\n=== 验证请求格式: ${name} ===`);
    console.log('请求参数:');
    console.log(JSON.stringify(request, null, 2));
    
    const issues = [];
    
    // 检查必需字段
    if (!request.model) issues.push('缺少 model 字段');
    if (!request.messages || !Array.isArray(request.messages)) issues.push('缺少或无效的 messages 字段');
    
    // 检查可能导致错误的字段
    if (request.stream === undefined) issues.push('建议明确设置 stream 字段');
    if (request.enable_thinking === undefined) issues.push('建议明确设置 enable_thinking 字段');
    
    // 检查非流式调用的要求
    if (request.stream !== false && request.enable_thinking !== false) {
        issues.push('对于非流式调用，enable_thinking 必须设置为 false');
    }
    
    if (issues.length === 0) {
        console.log('✅ 请求格式正确');
    } else {
        console.log('❌ 发现问题:');
        issues.forEach(issue => console.log(`  - ${issue}`));
    }
    
    return issues.length === 0;
}

function testRequestFormats() {
    console.log('开始验证AI请求格式...');
    
    const correctResult = validateRequest(correctRequest, '修复后的请求');
    const incorrectResult = validateRequest(incorrectRequest, '原始请求');
    
    console.log('\n=== 总结 ===');
    console.log(`修复后的请求: ${correctResult ? '✅ 通过' : '❌ 失败'}`);
    console.log(`原始请求: ${incorrectResult ? '✅ 通过' : '❌ 失败'}`);
    
    if (correctResult && !incorrectResult) {
        console.log('🎉 修复成功！新的请求格式应该能够正常工作');
    } else if (!correctResult) {
        console.log('⚠️  修复后的请求仍有问题，需要进一步调整');
    } else {
        console.log('ℹ️  两种格式都通过验证');
    }
}

function generateSystemPrompt() {
    return `你是一个专业的测试自动化专家，负责将自然语言的测试描述转换为结构化的YAML格式测试步骤。

请根据用户提供的测试文档内容，生成符合以下格式的YAML测试步骤：

\`\`\`yaml
name: "测试名称"
description: "测试描述"
target: "目标网站URL"
steps:
  - goal: "步骤目标描述"
    operation: "操作类型"
    content: "操作内容"
\`\`\`

操作类型包括：
- "导航": 访问网页或URL
- "点击": 点击按钮、链接或元素
- "填写": 输入文本到表单字段
- "检查": 验证页面内容或状态
- "等待": 等待页面加载或元素出现
- "截图": 捕获页面截图

要求：
1. 仔细分析测试文档，理解测试目标和流程
2. 将测试步骤分解为清晰、可执行的操作
3. 确保每个步骤都有明确的目标和操作类型
4. 生成的YAML格式必须正确且完整
5. 只返回YAML内容，不要包含其他解释文字`;
}

function testPromptGeneration() {
    console.log('\n=== 测试提示词生成 ===');
    
    const systemPrompt = generateSystemPrompt();
    const testContent = `# 用户登录测试

## 测试目标
验证用户能够成功登录系统

## 测试步骤
1. 访问登录页面
2. 输入用户名和密码
3. 点击登录按钮
4. 验证登录成功`;

    const userPrompt = `请为以下测试文档生成YAML格式的测试步骤：

${testContent}

请生成完整的YAML测试步骤文档。`;

    const fullRequest = {
        model: 'gpt-3.5-turbo',
        messages: [
            {
                role: 'system',
                content: systemPrompt
            },
            {
                role: 'user',
                content: userPrompt
            }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        stream: false,
        enable_thinking: false
    };

    console.log('生成的完整请求:');
    console.log('- 系统提示词长度:', systemPrompt.length);
    console.log('- 用户提示词长度:', userPrompt.length);
    console.log('- 总消息数:', fullRequest.messages.length);
    console.log('- 请求参数:', {
        model: fullRequest.model,
        temperature: fullRequest.temperature,
        max_tokens: fullRequest.max_tokens,
        stream: fullRequest.stream,
        enable_thinking: fullRequest.enable_thinking
    });

    const isValid = validateRequest(fullRequest, '完整的步骤生成请求');
    console.log(`完整请求验证: ${isValid ? '✅ 通过' : '❌ 失败'}`);
    
    return isValid;
}

function main() {
    console.log('AI请求调试工具');
    console.log('================');
    
    testRequestFormats();
    const promptTest = testPromptGeneration();
    
    console.log('\n=== 最终结果 ===');
    if (promptTest) {
        console.log('🎉 所有测试通过！AI服务请求应该能够正常工作');
        console.log('');
        console.log('修复要点:');
        console.log('1. ✅ 添加了 stream: false 参数');
        console.log('2. ✅ 添加了 enable_thinking: false 参数');
        console.log('3. ✅ 统一了请求格式创建方法');
        console.log('4. ✅ 改进了错误处理和调试信息');
    } else {
        console.log('❌ 仍有问题需要解决');
    }
}

// 运行测试
if (require.main === module) {
    main();
}

module.exports = {
    validateRequest,
    testRequestFormats,
    testPromptGeneration,
    generateSystemPrompt
};
